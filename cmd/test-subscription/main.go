package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"crawler/pkg/proxy"
)

func main() {
	fmt.Println("=== 订阅链接测试程序 ===")
	
	// 创建 Shadowsocks 提供者
	provider := proxy.NewShadowsocksProvider()
	fmt.Println("✓ Shadowsocks 提供者创建成功")
	
	// 测试订阅链接
	subscriptionURL := "https://onlysub.mjurl.com/api/v1/client/subscribe?token=6574374f99280ca9203974f736ab1935"
	fmt.Printf("测试订阅链接: %s\n", subscriptionURL)
	
	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	
	fmt.Println("开始解析订阅...")
	
	// 解析订阅
	proxies, err := provider.ParseSubscription(ctx, subscriptionURL)
	if err != nil {
		log.Printf("❌ 订阅解析失败: %v", err)
		
		// 尝试一些常见的测试订阅链接
		fmt.Println("\n尝试其他测试方法...")
		testAlternativeSubscriptions(provider)
		return
	}
	
	fmt.Printf("✅ 订阅解析成功！共解析到 %d 个代理\n", len(proxies))
	
	if len(proxies) == 0 {
		fmt.Println("⚠️  订阅中没有找到有效的代理配置")
		return
	}
	
	// 显示解析到的代理信息
	fmt.Println("\n=== 解析到的代理列表 ===")
	displayCount := 10
	if len(proxies) < displayCount {
		displayCount = len(proxies)
	}
	
	for i := 0; i < displayCount; i++ {
		p := proxies[i]
		config := p.GetConfig()
		
		fmt.Printf("%d. %s:%d\n", i+1, p.GetAddress(), p.GetPort())
		fmt.Printf("   类型: %s\n", p.GetType())
		fmt.Printf("   方法: %v\n", config["method"])
		fmt.Printf("   ID: %s\n", p.GetID())
		fmt.Println()
	}
	
	if len(proxies) > displayCount {
		fmt.Printf("... 还有 %d 个代理未显示\n", len(proxies)-displayCount)
	}
	
	// 验证代理配置
	fmt.Println("\n=== 验证代理配置 ===")
	validCount := 0
	for i, p := range proxies {
		if i >= 5 { // 只验证前5个
			break
		}
		
		err := provider.ValidateProxy(p)
		if err != nil {
			fmt.Printf("代理 %d 验证失败: %v\n", i+1, err)
		} else {
			fmt.Printf("代理 %d 验证通过 ✅\n", i+1)
			validCount++
		}
	}
	
	fmt.Printf("\n前5个代理中有 %d 个通过验证\n", validCount)
	
	// 测试代理管理器集成
	fmt.Println("\n=== 测试代理管理器集成 ===")
	testProxyManager(provider, proxies)
}

func testAlternativeSubscriptions(provider proxy.ProxyProvider) {
	// 测试一些公开的测试订阅链接或者手动创建代理
	fmt.Println("创建测试代理进行演示...")
	
	// 手动创建一些测试代理配置
	testConfigs := []map[string]interface{}{
		{
			"address":  "example1.com",
			"port":     8388,
			"password": "password123",
			"method":   "aes-256-gcm",
		},
		{
			"address":  "example2.com",
			"port":     8389,
			"password": "password456",
			"method":   "chacha20-ietf-poly1305",
		},
	}
	
	var testProxies []proxy.Proxy
	for i, config := range testConfigs {
		p, err := provider.CreateProxy(config)
		if err != nil {
			fmt.Printf("创建测试代理 %d 失败: %v\n", i+1, err)
			continue
		}
		
		testProxies = append(testProxies, p)
		fmt.Printf("✅ 测试代理 %d: %s:%d\n", i+1, p.GetAddress(), p.GetPort())
	}
	
	if len(testProxies) > 0 {
		fmt.Println("\n使用测试代理演示管理器功能...")
		testProxyManager(provider, testProxies)
	}
}

func testProxyManager(provider proxy.ProxyProvider, proxies []proxy.Proxy) {
	// 创建代理管理器
	config := proxy.DefaultProxyManagerConfig()
	config.HealthCheckInterval = 30 * time.Second
	config.HealthCheckTimeout = 5 * time.Second
	
	manager := proxy.NewProxyManager(config)
	
	// 添加提供者
	if err := manager.AddProvider(provider); err != nil {
		log.Printf("添加提供者失败: %v", err)
		return
	}
	
	// 添加代理到管理器
	addedCount := 0
	for i, p := range proxies {
		if i >= 5 { // 只添加前5个代理
			break
		}
		
		if err := manager.AddProxy(p); err != nil {
			fmt.Printf("添加代理 %d 失败: %v\n", i+1, err)
			continue
		}
		addedCount++
	}
	
	fmt.Printf("✅ 成功添加 %d 个代理到管理器\n", addedCount)
	
	// 设置过滤器（暂时不过滤，因为代理状态都是未知的）
	manager.SetProxySelector(proxy.NewRandomSelector())
	
	// 显示管理器状态
	fmt.Printf("管理器状态:\n")
	fmt.Printf("  总代理数: %d\n", manager.GetProxyCount())
	fmt.Printf("  健康代理数: %d\n", manager.GetHealthyProxyCount())
	
	// 获取所有代理
	allProxies := manager.GetAllProxies()
	fmt.Printf("  实际获取到的代理数: %d\n", len(allProxies))
	
	// 尝试获取一个代理（不过滤健康状态）
	if len(allProxies) > 0 {
		// 临时移除健康过滤器
		manager.SetProxyFilter(nil)
		
		selectedProxy, err := manager.GetRandomProxy()
		if err != nil {
			fmt.Printf("获取随机代理失败: %v\n", err)
		} else {
			fmt.Printf("✅ 随机选择的代理: %s:%d\n", 
				selectedProxy.GetAddress(), selectedProxy.GetPort())
		}
	}
	
	// 简单的健康检查演示
	if len(allProxies) > 0 {
		fmt.Println("\n进行简单的健康检查演示...")
		checker := proxy.NewDefaultHealthChecker(3 * time.Second)
		
		testProxy := allProxies[0]
		fmt.Printf("检查代理: %s:%d\n", testProxy.GetAddress(), testProxy.GetPort())
		
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		err := checker.Check(ctx, testProxy)
		if err != nil {
			fmt.Printf("❌ 健康检查失败: %v\n", err)
			testProxy.SetStatus(proxy.ProxyStatusUnhealthy)
		} else {
			fmt.Printf("✅ 健康检查通过\n")
			testProxy.SetStatus(proxy.ProxyStatusHealthy)
		}
		
		fmt.Printf("代理状态: %s\n", testProxy.GetStatus().String())
	}
	
	fmt.Println("✅ 代理管理器测试完成")
}
