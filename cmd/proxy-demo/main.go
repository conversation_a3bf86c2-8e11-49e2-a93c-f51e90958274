package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"crawler/pkg/proxy"
)

func main() {
	fmt.Println("=== 代理管理系统演示 ===")
	
	// 1. 创建代理管理器
	config := &proxy.ProxyManagerConfig{
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  5 * time.Second,
		MaxRetries:          3,
		EnableHealthCheck:   true,
		ProxyTimeout:        10 * time.Second,
	}
	
	manager := proxy.NewProxyManager(config)
	fmt.Println("✓ 代理管理器创建成功")
	
	// 2. 添加 Shadowsocks 提供者
	ssProvider := proxy.NewShadowsocksProvider()
	if err := manager.AddProvider(ssProvider); err != nil {
		log.Fatalf("添加 SS 提供者失败: %v", err)
	}
	fmt.Println("✓ Shadowsocks 提供者添加成功")
	
	// 3. 导入真实的机场订阅链接
	subscriptionURL := "https://onlysub.mjurl.com/api/v1/client/subscribe?token=6574374f99280ca9203974f736ab1935"

	fmt.Println("✓ 开始导入机场订阅...")
	fmt.Printf("订阅链接: %s\n", subscriptionURL)

	ctx := context.Background()
	err := manager.ImportSubscription(ctx, subscriptionURL, proxy.ProxyTypeShadowsocks)
	if err != nil {
		log.Printf("导入订阅失败: %v", err)

		// 如果订阅导入失败，创建一些测试代理作为备用
		fmt.Println("使用测试代理作为备用...")
		testProxies := []map[string]interface{}{
			{
				"address":  "*******",
				"port":     8388,
				"password": "password123",
				"method":   "aes-256-gcm",
			},
			{
				"address":  "*******",
				"port":     8389,
				"password": "password456",
				"method":   "chacha20-ietf-poly1305",
			},
		}

		for i, config := range testProxies {
			testProxy, err := ssProvider.CreateProxy(config)
			if err != nil {
				log.Printf("创建测试代理 %d 失败: %v", i+1, err)
				continue
			}

			// 设置为健康状态
			testProxy.SetStatus(proxy.ProxyStatusHealthy)

			// 添加到管理器
			if err := manager.AddProxy(testProxy); err != nil {
				log.Printf("添加测试代理 %d 失败: %v", i+1, err)
				continue
			}

			fmt.Printf("  - 测试代理 %d: %s:%d\n",
				i+1, testProxy.GetAddress(), testProxy.GetPort())
		}
	} else {
		fmt.Printf("✓ 订阅导入成功，共导入 %d 个代理\n", manager.GetProxyCount())

		// 显示导入的代理信息
		allProxies := manager.GetAllProxies()
		fmt.Println("导入的代理列表:")
		for i, p := range allProxies {
			if i >= 5 { // 只显示前5个
				fmt.Printf("  ... 还有 %d 个代理\n", len(allProxies)-5)
				break
			}
			fmt.Printf("  %d. %s:%d [%s]\n",
				i+1, p.GetAddress(), p.GetPort(), p.GetType())
		}
	}
	
	// 4. 设置过滤器和选择器
	manager.SetProxyFilter(proxy.NewHealthyFilter())
	manager.SetProxySelector(proxy.NewRandomSelector())
	fmt.Println("✓ 过滤器和选择器设置完成")

	// 5. 启动健康检查（如果有代理的话）
	if manager.GetProxyCount() > 0 {
		fmt.Println("✓ 启动健康检查...")

		// 启动后台健康检查
		healthCtx, healthCancel := context.WithCancel(context.Background())
		defer healthCancel()

		if err := manager.StartHealthCheck(healthCtx, 30*time.Second); err != nil {
			log.Printf("启动健康检查失败: %v", err)
		} else {
			fmt.Println("✓ 健康检查已启动")

			// 等待一小段时间让健康检查运行
			fmt.Println("等待健康检查完成...")
			time.Sleep(3 * time.Second)

			// 手动检查前几个代理的健康状态
			allProxies := manager.GetAllProxies()
			checker := proxy.NewDefaultHealthChecker(5 * time.Second)

			checkCount := 3
			if len(allProxies) < checkCount {
				checkCount = len(allProxies)
			}

			fmt.Printf("手动检查前 %d 个代理的健康状态:\n", checkCount)
			for i := 0; i < checkCount; i++ {
				p := allProxies[i]
				checkCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)

				fmt.Printf("  检查代理 %s:%d ... ", p.GetAddress(), p.GetPort())
				err := checker.Check(checkCtx, p)
				if err != nil {
					p.SetStatus(proxy.ProxyStatusUnhealthy)
					fmt.Printf("❌ 不健康 (%v)\n", err)
				} else {
					p.SetStatus(proxy.ProxyStatusHealthy)
					fmt.Printf("✅ 健康\n")
				}
				cancel()
			}
		}
	}
	
	// 6. 演示代理管理功能
	fmt.Println("\n=== 代理管理功能演示 ===")
	
	// 显示统计信息
	fmt.Printf("代理总数: %d\n", manager.GetProxyCount())
	fmt.Printf("健康代理数: %d\n", manager.GetHealthyProxyCount())
	
	// 获取所有代理
	allProxies := manager.GetAllProxies()
	fmt.Printf("所有代理列表 (%d 个):\n", len(allProxies))
	for i, p := range allProxies {
		fmt.Printf("  %d. %s:%d [%s] - %s\n", 
			i+1, p.GetAddress(), p.GetPort(), p.GetType(), p.GetStatus().String())
	}
	
	// 获取健康代理
	healthyProxies := manager.GetHealthyProxies()
	fmt.Printf("\n健康代理列表 (%d 个):\n", len(healthyProxies))
	for i, p := range healthyProxies {
		fmt.Printf("  %d. %s:%d [%s]\n", 
			i+1, p.GetAddress(), p.GetPort(), p.GetType())
	}
	
	// 7. 演示代理选择
	fmt.Println("\n=== 代理选择演示 ===")
	
	if len(healthyProxies) > 0 {
		// 随机选择代理
		for i := 0; i < 3; i++ {
			selectedProxy, err := manager.GetRandomProxy()
			if err != nil {
				log.Printf("获取随机代理失败: %v", err)
				continue
			}
			fmt.Printf("随机选择 %d: %s:%d\n", 
				i+1, selectedProxy.GetAddress(), selectedProxy.GetPort())
		}
	} else {
		fmt.Println("没有可用的健康代理")
	}
	
	// 8. 演示不同的选择策略
	fmt.Println("\n=== 选择策略演示 ===")
	
	if len(healthyProxies) > 0 {
		// 轮询选择器
		rrSelector := proxy.NewRoundRobinSelector()
		fmt.Println("轮询选择:")
		for i := 0; i < 5; i++ {
			selectedProxy, err := rrSelector.Select(healthyProxies)
			if err != nil {
				log.Printf("轮询选择失败: %v", err)
				continue
			}
			fmt.Printf("  轮询 %d: %s:%d\n", 
				i+1, selectedProxy.GetAddress(), selectedProxy.GetPort())
		}
	}
	
	// 9. 演示过滤器
	fmt.Println("\n=== 过滤器演示 ===")
	
	// 类型过滤器
	typeFilter := proxy.NewTypeFilter(proxy.ProxyTypeShadowsocks)
	ssProxies := typeFilter.Filter(allProxies)
	fmt.Printf("Shadowsocks 代理数量: %d\n", len(ssProxies))
	
	// 健康过滤器
	healthyFilter := proxy.NewHealthyFilter()
	filteredHealthy := healthyFilter.Filter(allProxies)
	fmt.Printf("过滤后的健康代理数量: %d\n", len(filteredHealthy))
	
	// 10. 演示错误处理
	fmt.Println("\n=== 错误处理演示 ===")
	
	// 尝试获取不存在的代理
	_, err = manager.GetProxyByID("non-existent-id")
	if err != nil {
		fmt.Printf("预期错误 - 获取不存在的代理: %v\n", err)
	}
	
	// 尝试导入无效订阅
	ctx = context.Background()
	err = manager.ImportSubscription(ctx, "invalid-url", proxy.ProxyTypeShadowsocks)
	if err != nil {
		fmt.Printf("预期错误 - 无效订阅链接: %v\n", err)
	}
	
	// 11. 演示单独的健康检查
	fmt.Println("\n=== 健康检查演示 ===")
	
	checker := proxy.NewDefaultHealthChecker(3 * time.Second)
	if len(allProxies) > 0 {
		testProxy := allProxies[0]
		fmt.Printf("检查代理: %s:%d\n", testProxy.GetAddress(), testProxy.GetPort())
		
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		err := checker.Check(ctx, testProxy)
		if err != nil {
			fmt.Printf("健康检查失败: %v\n", err)
		} else {
			fmt.Println("健康检查通过")
		}
	}
	
	// 12. 清理演示
	fmt.Println("\n=== 清理演示 ===")
	
	// 清空所有代理
	if err := manager.ClearProxies(); err != nil {
		log.Printf("清空代理失败: %v", err)
	} else {
		fmt.Println("✓ 所有代理已清空")
		fmt.Printf("清空后代理数量: %d\n", manager.GetProxyCount())
	}
	
	// 移除提供者
	if err := manager.RemoveProvider("shadowsocks"); err != nil {
		log.Printf("移除提供者失败: %v", err)
	} else {
		fmt.Println("✓ Shadowsocks 提供者已移除")
	}
	
	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("代理管理系统功能演示结束。")
	fmt.Println("在实际使用中，您可以:")
	fmt.Println("1. 导入真实的机场订阅链接")
	fmt.Println("2. 启用自动健康检查")
	fmt.Println("3. 在爬虫中使用代理进行网络请求")
	fmt.Println("4. 根据需要自定义过滤器和选择器")
}
