package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"crawler/pkg/proxy"
)

func main() {
	fmt.Println("=== 代理管理系统演示 ===")
	
	// 1. 创建代理管理器
	config := &proxy.ProxyManagerConfig{
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  5 * time.Second,
		MaxRetries:          3,
		EnableHealthCheck:   true,
		ProxyTimeout:        10 * time.Second,
	}
	
	manager := proxy.NewProxyManager(config)
	fmt.Println("✓ 代理管理器创建成功")
	
	// 2. 添加 Shadowsocks 提供者
	ssProvider := proxy.NewShadowsocksProvider()
	if err := manager.AddProvider(ssProvider); err != nil {
		log.Fatalf("添加 SS 提供者失败: %v", err)
	}
	fmt.Println("✓ Shadowsocks 提供者添加成功")
	
	// 3. 手动创建一些测试代理（模拟从订阅导入）
	testProxies := []map[string]interface{}{
		{
			"address":  "*******",
			"port":     8388,
			"password": "password123",
			"method":   "aes-256-gcm",
		},
		{
			"address":  "*******",
			"port":     8389,
			"password": "password456",
			"method":   "chacha20-ietf-poly1305",
		},
		{
			"address":  "**********",
			"port":     8390,
			"password": "password789",
			"method":   "aes-128-gcm",
		},
	}
	
	// 创建代理并添加到管理器
	fmt.Println("✓ 创建测试代理...")
	for i, config := range testProxies {
		testProxy, err := ssProvider.CreateProxy(config)
		if err != nil {
			log.Printf("创建代理 %d 失败: %v", i+1, err)
			continue
		}

		// 模拟健康状态（实际使用中会通过健康检查设置）
		if i < 2 {
			testProxy.SetStatus(proxy.ProxyStatusHealthy)
		} else {
			testProxy.SetStatus(proxy.ProxyStatusUnhealthy)
		}

		// 添加到管理器
		if err := manager.AddProxy(testProxy); err != nil {
			log.Printf("添加代理 %d 失败: %v", i+1, err)
			continue
		}

		fmt.Printf("  - 代理 %d: %s:%d (%s)\n",
			i+1, testProxy.GetAddress(), testProxy.GetPort(), testProxy.GetStatus().String())
	}
	
	// 4. 设置过滤器和选择器
	manager.SetProxyFilter(proxy.NewHealthyFilter())
	manager.SetProxySelector(proxy.NewRandomSelector())
	fmt.Println("✓ 过滤器和选择器设置完成")
	
	// 5. 演示代理管理功能
	fmt.Println("\n=== 代理管理功能演示 ===")
	
	// 显示统计信息
	fmt.Printf("代理总数: %d\n", manager.GetProxyCount())
	fmt.Printf("健康代理数: %d\n", manager.GetHealthyProxyCount())
	
	// 获取所有代理
	allProxies := manager.GetAllProxies()
	fmt.Printf("所有代理列表 (%d 个):\n", len(allProxies))
	for i, p := range allProxies {
		fmt.Printf("  %d. %s:%d [%s] - %s\n", 
			i+1, p.GetAddress(), p.GetPort(), p.GetType(), p.GetStatus().String())
	}
	
	// 获取健康代理
	healthyProxies := manager.GetHealthyProxies()
	fmt.Printf("\n健康代理列表 (%d 个):\n", len(healthyProxies))
	for i, p := range healthyProxies {
		fmt.Printf("  %d. %s:%d [%s]\n", 
			i+1, p.GetAddress(), p.GetPort(), p.GetType())
	}
	
	// 6. 演示代理选择
	fmt.Println("\n=== 代理选择演示 ===")
	
	if len(healthyProxies) > 0 {
		// 随机选择代理
		for i := 0; i < 3; i++ {
			selectedProxy, err := manager.GetRandomProxy()
			if err != nil {
				log.Printf("获取随机代理失败: %v", err)
				continue
			}
			fmt.Printf("随机选择 %d: %s:%d\n", 
				i+1, selectedProxy.GetAddress(), selectedProxy.GetPort())
		}
	} else {
		fmt.Println("没有可用的健康代理")
	}
	
	// 7. 演示不同的选择策略
	fmt.Println("\n=== 选择策略演示 ===")
	
	if len(healthyProxies) > 0 {
		// 轮询选择器
		rrSelector := proxy.NewRoundRobinSelector()
		fmt.Println("轮询选择:")
		for i := 0; i < 5; i++ {
			selectedProxy, err := rrSelector.Select(healthyProxies)
			if err != nil {
				log.Printf("轮询选择失败: %v", err)
				continue
			}
			fmt.Printf("  轮询 %d: %s:%d\n", 
				i+1, selectedProxy.GetAddress(), selectedProxy.GetPort())
		}
	}
	
	// 8. 演示过滤器
	fmt.Println("\n=== 过滤器演示 ===")
	
	// 类型过滤器
	typeFilter := proxy.NewTypeFilter(proxy.ProxyTypeShadowsocks)
	ssProxies := typeFilter.Filter(allProxies)
	fmt.Printf("Shadowsocks 代理数量: %d\n", len(ssProxies))
	
	// 健康过滤器
	healthyFilter := proxy.NewHealthyFilter()
	filteredHealthy := healthyFilter.Filter(allProxies)
	fmt.Printf("过滤后的健康代理数量: %d\n", len(filteredHealthy))
	
	// 9. 演示错误处理
	fmt.Println("\n=== 错误处理演示 ===")
	
	// 尝试获取不存在的代理
	_, err := manager.GetProxyByID("non-existent-id")
	if err != nil {
		fmt.Printf("预期错误 - 获取不存在的代理: %v\n", err)
	}
	
	// 尝试导入无效订阅
	ctx := context.Background()
	err = manager.ImportSubscription(ctx, "invalid-url", proxy.ProxyTypeShadowsocks)
	if err != nil {
		fmt.Printf("预期错误 - 无效订阅链接: %v\n", err)
	}
	
	// 10. 演示健康检查（简短演示）
	fmt.Println("\n=== 健康检查演示 ===")
	
	checker := proxy.NewDefaultHealthChecker(3 * time.Second)
	if len(allProxies) > 0 {
		testProxy := allProxies[0]
		fmt.Printf("检查代理: %s:%d\n", testProxy.GetAddress(), testProxy.GetPort())
		
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		err := checker.Check(ctx, testProxy)
		if err != nil {
			fmt.Printf("健康检查失败: %v\n", err)
		} else {
			fmt.Println("健康检查通过")
		}
	}
	
	// 11. 清理演示
	fmt.Println("\n=== 清理演示 ===")
	
	// 清空所有代理
	if err := manager.ClearProxies(); err != nil {
		log.Printf("清空代理失败: %v", err)
	} else {
		fmt.Println("✓ 所有代理已清空")
		fmt.Printf("清空后代理数量: %d\n", manager.GetProxyCount())
	}
	
	// 移除提供者
	if err := manager.RemoveProvider("shadowsocks"); err != nil {
		log.Printf("移除提供者失败: %v", err)
	} else {
		fmt.Println("✓ Shadowsocks 提供者已移除")
	}
	
	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("代理管理系统功能演示结束。")
	fmt.Println("在实际使用中，您可以:")
	fmt.Println("1. 导入真实的机场订阅链接")
	fmt.Println("2. 启用自动健康检查")
	fmt.Println("3. 在爬虫中使用代理进行网络请求")
	fmt.Println("4. 根据需要自定义过滤器和选择器")
}
