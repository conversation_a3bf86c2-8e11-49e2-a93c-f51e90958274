# 代理管理系统

这是一个用于分布式网络爬虫系统的代理管理模块，支持多种代理协议和智能管理功能。

## 功能特性

- **多协议支持**: 支持 Shadowsocks、SSR、VMess、Trojan 等代理协议
- **机场订阅**: 支持导入和解析机场订阅链接
- **智能选择**: 提供随机选择、轮询等多种代理选择策略
- **健康检查**: 自动检测代理健康状态，确保可用性
- **过滤器**: 支持按类型、状态等条件过滤代理
- **线程安全**: 支持并发访问，适用于高并发场景
- **可扩展**: 模块化设计，易于扩展新的代理类型和功能

## 核心接口

### ProxyManager - 代理管理器
```go
type ProxyManager interface {
    // 添加代理提供者
    AddProvider(provider ProxyProvider) error
    
    // 导入机场订阅链接
    ImportSubscription(ctx context.Context, subscriptionURL string, providerType ProxyType) error
    
    // 随机获取一个健康的代理
    GetRandomProxy() (Proxy, error)
    
    // 启动健康检查
    StartHealthCheck(ctx context.Context, interval time.Duration) error
    
    // 设置代理过滤器和选择器
    SetProxyFilter(filter ProxyFilter)
    SetProxySelector(selector ProxySelector)
}
```

### Proxy - 代理接口
```go
type Proxy interface {
    GetID() string
    GetType() ProxyType
    GetAddress() string
    GetPort() int
    GetStatus() ProxyStatus
    IsHealthy() bool
}
```

### ProxyProvider - 代理提供者
```go
type ProxyProvider interface {
    GetName() string
    GetType() ProxyType
    ParseSubscription(ctx context.Context, subscriptionURL string) ([]Proxy, error)
    ValidateProxy(proxy Proxy) error
}
```

## 快速开始

### 1. 创建代理管理器

```go
package main

import (
    "context"
    "log"
    "time"
    "your-project/pkg/proxy"
)

func main() {
    // 创建配置
    config := &proxy.ProxyManagerConfig{
        HealthCheckInterval: 5 * time.Minute,
        HealthCheckTimeout:  10 * time.Second,
        EnableHealthCheck:   true,
    }
    
    // 创建管理器
    manager := proxy.NewProxyManager(config)
}
```

### 2. 添加代理提供者

```go
// 添加 Shadowsocks 提供者
ssProvider := proxy.NewShadowsocksProvider()
if err := manager.AddProvider(ssProvider); err != nil {
    log.Fatal(err)
}
```

### 3. 导入机场订阅

```go
ctx := context.Background()
subscriptionURL := "https://your-airport.com/subscription"

err := manager.ImportSubscription(ctx, subscriptionURL, proxy.ProxyTypeShadowsocks)
if err != nil {
    log.Printf("Failed to import subscription: %v", err)
}
```

### 4. 设置过滤器和选择器

```go
// 只使用健康的代理
manager.SetProxyFilter(proxy.NewHealthyFilter())

// 使用随机选择策略
manager.SetProxySelector(proxy.NewRandomSelector())
```

### 5. 启动健康检查

```go
// 每分钟检查一次代理健康状态
err := manager.StartHealthCheck(ctx, 1*time.Minute)
if err != nil {
    log.Printf("Failed to start health check: %v", err)
}
```

### 6. 获取和使用代理

```go
// 获取一个随机的健康代理
proxy, err := manager.GetRandomProxy()
if err != nil {
    log.Printf("No healthy proxy available: %v", err)
    return
}

fmt.Printf("Using proxy: %s:%d\n", proxy.GetAddress(), proxy.GetPort())

// 使用代理进行网络请求
// ... 你的网络请求代码
```

## 高级用法

### 自定义代理选择器

```go
type WeightedSelector struct {
    weights map[string]int
}

func (ws *WeightedSelector) Select(proxies []proxy.Proxy) (proxy.Proxy, error) {
    // 实现基于权重的选择逻辑
    // ...
}

// 使用自定义选择器
manager.SetProxySelector(&WeightedSelector{
    weights: map[string]int{
        "high-speed": 3,
        "normal":     1,
    },
})
```

### 自定义过滤器

```go
type RegionFilter struct {
    allowedRegions []string
}

func (rf *RegionFilter) Filter(proxies []proxy.Proxy) []proxy.Proxy {
    // 实现基于地区的过滤逻辑
    // ...
}

// 使用自定义过滤器
manager.SetProxyFilter(&RegionFilter{
    allowedRegions: []string{"US", "JP", "SG"},
})
```

### 组合过滤器

```go
type CompositeFilter struct {
    filters []proxy.ProxyFilter
}

func (cf *CompositeFilter) Filter(proxies []proxy.Proxy) []proxy.Proxy {
    result := proxies
    for _, filter := range cf.filters {
        result = filter.Filter(result)
    }
    return result
}

// 组合多个过滤器
compositeFilter := &CompositeFilter{
    filters: []proxy.ProxyFilter{
        proxy.NewHealthyFilter(),
        proxy.NewTypeFilter(proxy.ProxyTypeShadowsocks),
        &RegionFilter{allowedRegions: []string{"US", "JP"}},
    },
}
manager.SetProxyFilter(compositeFilter)
```

## 错误处理

系统定义了多种错误类型，便于错误处理：

```go
// 检查特定错误类型
proxy, err := manager.GetRandomProxy()
if err != nil {
    switch err {
    case proxy.ErrNoHealthyProxy:
        log.Println("没有可用的健康代理")
        // 可能需要等待健康检查完成或导入新的代理
    case proxy.ErrProxyNotFound:
        log.Println("代理未找到")
    default:
        log.Printf("其他错误: %v", err)
    }
}
```

## 监控和统计

```go
// 获取代理统计信息
fmt.Printf("总代理数: %d\n", manager.GetProxyCount())
fmt.Printf("健康代理数: %d\n", manager.GetHealthyProxyCount())

// 按类型获取代理
ssProxies := manager.GetProxiesByType(proxy.ProxyTypeShadowsocks)
fmt.Printf("Shadowsocks 代理数: %d\n", len(ssProxies))
```

## 最佳实践

1. **健康检查**: 始终启用健康检查以确保代理可用性
2. **错误处理**: 妥善处理各种错误情况，特别是网络相关错误
3. **资源清理**: 程序退出时记得停止健康检查
4. **并发安全**: 所有接口都是线程安全的，可以在多个 goroutine 中使用
5. **配置优化**: 根据实际需求调整健康检查间隔和超时时间

## 扩展开发

### 添加新的代理类型

1. 实现 `Proxy` 接口
2. 实现 `ProxyProvider` 接口
3. 在 `types.go` 中添加新的代理类型常量
4. 在健康检查器中添加对应的检查逻辑

### 添加新的选择策略

实现 `ProxySelector` 接口：

```go
type CustomSelector struct {
    // 自定义字段
}

func (cs *CustomSelector) Select(proxies []proxy.Proxy) (proxy.Proxy, error) {
    // 实现自定义选择逻辑
}
```

## 注意事项

- 订阅链接的格式可能因机场而异，当前实现支持常见的 Base64 编码格式
- 健康检查可能会产生网络流量，请根据实际情况调整检查频率
- 某些代理协议可能需要额外的依赖库来实现完整的连接测试
- 在生产环境中使用时，建议添加适当的日志记录和监控

## 许可证

MIT License
