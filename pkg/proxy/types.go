package proxy

import (
	"errors"
	"fmt"
	"time"
)

// 错误定义
var (
	ErrProxyNotFound        = errors.New("proxy not found")
	ErrProviderNotFound     = errors.New("provider not found")
	ErrProviderExists       = errors.New("provider already exists")
	ErrInvalidSubscription  = errors.New("invalid subscription URL")
	ErrNoHealthyProxy       = errors.New("no healthy proxy available")
	ErrInvalidProxyConfig   = errors.New("invalid proxy configuration")
	ErrProxyTimeout         = errors.New("proxy connection timeout")
	ErrHealthCheckFailed    = errors.New("health check failed")
	ErrEmptyProxyList       = errors.New("empty proxy list")
	ErrInvalidProxyType     = errors.New("invalid proxy type")
)

// ProxyError 代理错误类型
type ProxyError struct {
	Type    string
	Message string
	Cause   error
}

func (e *ProxyError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

func (e *ProxyError) Unwrap() error {
	return e.Cause
}

// NewProxyError 创建新的代理错误
func NewProxyError(errorType, message string, cause error) *ProxyError {
	return &ProxyError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// ProxyStats 代理统计信息
type ProxyStats struct {
	TotalProxies   int           `json:"total_proxies"`
	HealthyProxies int           `json:"healthy_proxies"`
	UnhealthyProxies int         `json:"unhealthy_proxies"`
	LastUpdate     time.Time     `json:"last_update"`
	ProviderStats  map[string]int `json:"provider_stats"`
}

// ProxyInfo 代理信息
type ProxyInfo struct {
	ID          string                 `json:"id"`
	Type        ProxyType              `json:"type"`
	Address     string                 `json:"address"`
	Port        int                    `json:"port"`
	Status      ProxyStatus            `json:"status"`
	LastCheck   time.Time              `json:"last_check"`
	Config      map[string]interface{} `json:"config"`
	Provider    string                 `json:"provider"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// SubscriptionInfo 订阅信息
type SubscriptionInfo struct {
	URL         string    `json:"url"`
	Type        ProxyType `json:"type"`
	LastUpdate  time.Time `json:"last_update"`
	ProxyCount  int       `json:"proxy_count"`
	Status      string    `json:"status"`
	ErrorMsg    string    `json:"error_msg,omitempty"`
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	ProxyID     string        `json:"proxy_id"`
	Status      ProxyStatus   `json:"status"`
	ResponseTime time.Duration `json:"response_time"`
	Error       string        `json:"error,omitempty"`
	CheckTime   time.Time     `json:"check_time"`
}

// ProxyMetrics 代理指标
type ProxyMetrics struct {
	ProxyID      string        `json:"proxy_id"`
	RequestCount int64         `json:"request_count"`
	SuccessCount int64         `json:"success_count"`
	FailureCount int64         `json:"failure_count"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastUsed     time.Time     `json:"last_used"`
}

// String 方法实现
func (pt ProxyType) String() string {
	return string(pt)
}

func (ps ProxyStatus) String() string {
	switch ps {
	case ProxyStatusHealthy:
		return "healthy"
	case ProxyStatusUnhealthy:
		return "unhealthy"
	case ProxyStatusTimeout:
		return "timeout"
	default:
		return "unknown"
	}
}

// IsValid 检查代理类型是否有效
func (pt ProxyType) IsValid() bool {
	switch pt {
	case ProxyTypeShadowsocks, ProxyTypeSSR, ProxyTypeVMess, ProxyTypeTrojan:
		return true
	default:
		return false
	}
}

// IsHealthy 检查代理状态是否健康
func (ps ProxyStatus) IsHealthy() bool {
	return ps == ProxyStatusHealthy
}

// ParseProxyType 解析代理类型字符串
func ParseProxyType(s string) (ProxyType, error) {
	pt := ProxyType(s)
	if !pt.IsValid() {
		return "", ErrInvalidProxyType
	}
	return pt, nil
}

// ValidateProxyInfo 验证代理信息
func ValidateProxyInfo(info *ProxyInfo) error {
	if info == nil {
		return errors.New("proxy info is nil")
	}
	
	if info.ID == "" {
		return errors.New("proxy ID is empty")
	}
	
	if !info.Type.IsValid() {
		return fmt.Errorf("invalid proxy type: %s", info.Type)
	}
	
	if info.Address == "" {
		return errors.New("proxy address is empty")
	}
	
	if info.Port <= 0 || info.Port > 65535 {
		return fmt.Errorf("invalid proxy port: %d", info.Port)
	}
	
	return nil
}
