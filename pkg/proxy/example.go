package proxy

import (
	"context"
	"fmt"
	"log"
	"time"
)

// ExampleUsage 展示代理管理器的使用方法
func ExampleUsage() {
	// 1. 创建代理管理器配置
	config := &ProxyManagerConfig{
		HealthCheckInterval: 2 * time.Minute,
		HealthCheckTimeout:  10 * time.Second,
		MaxRetries:          3,
		EnableHealthCheck:   true,
		ProxyTimeout:        30 * time.Second,
	}
	
	// 2. 创建代理管理器
	manager := NewProxyManager(config)
	
	// 3. 添加 Shadowsocks 提供者
	ssProvider := NewShadowsocksProvider()
	if err := manager.AddProvider(ssProvider); err != nil {
		log.Printf("Failed to add SS provider: %v", err)
		return
	}
	
	// 4. 导入机场订阅链接
	ctx := context.Background()
	subscriptionURL := "https://example.com/ss-subscription"
	
	if err := manager.ImportSubscription(ctx, subscriptionURL, ProxyTypeShadowsocks); err != nil {
		log.Printf("Failed to import subscription: %v", err)
		// 这里可能会失败，因为示例 URL 不存在
	}
	
	// 5. 手动添加一些测试代理
	testProxies := []*ShadowsocksProxy{
		NewShadowsocksProxy("*******", 8388, "password123", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8389, "password456", "chacha20-ietf-poly1305"),
		NewShadowsocksProxy("**********", 8390, "password789", "aes-128-gcm"),
	}
	
	// 将测试代理添加到管理器
	for _, proxy := range testProxies {
		// 模拟健康状态
		proxy.SetStatus(ProxyStatusHealthy)
		
		// 这里需要通过提供者添加，或者直接操作内部状态
		// 为了示例，我们假设有一个内部方法来添加代理
		fmt.Printf("Test proxy created: %s (%s:%d)\n", 
			proxy.GetID(), proxy.GetAddress(), proxy.GetPort())
	}
	
	// 6. 设置过滤器和选择器
	manager.SetProxyFilter(NewHealthyFilter())
	manager.SetProxySelector(NewRandomSelector())
	
	// 7. 启动健康检查
	if err := manager.StartHealthCheck(ctx, 1*time.Minute); err != nil {
		log.Printf("Failed to start health check: %v", err)
	}
	
	// 8. 获取代理
	fmt.Println()
	fmt.Println("=== 代理管理器使用示例 ===")
	
	// 获取随机代理
	proxy, err := manager.GetRandomProxy()
	if err != nil {
		log.Printf("Failed to get random proxy: %v", err)
	} else {
		fmt.Printf("Random proxy: %s (%s:%d)\n", 
			proxy.GetID(), proxy.GetAddress(), proxy.GetPort())
	}
	
	// 获取所有代理
	allProxies := manager.GetAllProxies()
	fmt.Printf("Total proxies: %d\n", len(allProxies))
	
	// 获取健康代理
	healthyProxies := manager.GetHealthyProxies()
	fmt.Printf("Healthy proxies: %d\n", len(healthyProxies))
	
	// 按类型获取代理
	ssProxies := manager.GetProxiesByType(ProxyTypeShadowsocks)
	fmt.Printf("Shadowsocks proxies: %d\n", len(ssProxies))
	
	// 9. 代理统计信息
	fmt.Println()
	fmt.Println("=== 统计信息 ===")
	fmt.Printf("代理总数: %d\n", manager.GetProxyCount())
	fmt.Printf("健康代理数: %d\n", manager.GetHealthyProxyCount())
	
	// 10. 清理资源
	defer func() {
		if err := manager.StopHealthCheck(); err != nil {
			log.Printf("Failed to stop health check: %v", err)
		}
	}()
	
	// 等待一段时间让健康检查运行
	time.Sleep(5 * time.Second)
}

// ExampleCustomProvider 展示如何创建自定义代理提供者
func ExampleCustomProvider() {
	// 自定义提供者示例
	type CustomProvider struct {
		name string
	}
	
	provider := &CustomProvider{name: "custom"}
	
	fmt.Printf("Custom provider: %s\n", provider.name)
	
	// 这里可以实现 ProxyProvider 接口的所有方法
}

// ExampleHealthCheck 展示健康检查的使用
func ExampleHealthCheck() {
	// 创建健康检查器
	checker := NewDefaultHealthChecker(10 * time.Second)
	
	// 创建测试代理
	proxy := NewShadowsocksProxy("8.8.8.8", 53, "test", "aes-256-gcm")
	
	// 执行健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	
	if err := checker.Check(ctx, proxy); err != nil {
		fmt.Printf("Health check failed: %v\n", err)
		proxy.SetStatus(ProxyStatusUnhealthy)
	} else {
		fmt.Println("Health check passed")
		proxy.SetStatus(ProxyStatusHealthy)
	}
	
	fmt.Printf("Proxy status: %s\n", proxy.GetStatus().String())
}

// ExampleProxySelection 展示代理选择策略
func ExampleProxySelection() {
	// 创建测试代理列表
	proxies := []Proxy{
		NewShadowsocksProxy("*******", 8388, "pass1", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8388, "pass2", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8388, "pass3", "aes-256-gcm"),
	}
	
	// 设置为健康状态
	for _, proxy := range proxies {
		proxy.SetStatus(ProxyStatusHealthy)
	}
	
	fmt.Println()
	fmt.Println("=== 代理选择策略示例 ===")
	
	// 随机选择器
	randomSelector := NewRandomSelector()
	proxy, err := randomSelector.Select(proxies)
	if err != nil {
		log.Printf("Random selection failed: %v", err)
	} else {
		fmt.Printf("Random selected: %s\n", proxy.GetAddress())
	}
	
	// 轮询选择器
	rrSelector := NewRoundRobinSelector()
	for i := 0; i < 5; i++ {
		proxy, err := rrSelector.Select(proxies)
		if err != nil {
			log.Printf("Round robin selection failed: %v", err)
		} else {
			fmt.Printf("Round robin %d: %s\n", i+1, proxy.GetAddress())
		}
	}
}

// ExampleProxyFiltering 展示代理过滤功能
func ExampleProxyFiltering() {
	// 创建混合代理列表
	proxies := []Proxy{
		NewShadowsocksProxy("*******", 8388, "pass1", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8388, "pass2", "aes-256-gcm"),
	}
	
	// 设置不同的健康状态
	proxies[0].SetStatus(ProxyStatusHealthy)
	proxies[1].SetStatus(ProxyStatusUnhealthy)
	
	fmt.Println()
	fmt.Println("=== 代理过滤示例 ===")
	fmt.Printf("原始代理数量: %d\n", len(proxies))
	
	// 健康代理过滤器
	healthyFilter := NewHealthyFilter()
	healthyProxies := healthyFilter.Filter(proxies)
	fmt.Printf("健康代理数量: %d\n", len(healthyProxies))
	
	// 类型过滤器
	typeFilter := NewTypeFilter(ProxyTypeShadowsocks)
	ssProxies := typeFilter.Filter(proxies)
	fmt.Printf("Shadowsocks 代理数量: %d\n", len(ssProxies))
}

// ExampleErrorHandling 展示错误处理
func ExampleErrorHandling() {
	fmt.Println()
	fmt.Println("=== 错误处理示例 ===")
	
	manager := NewProxyManager(nil) // 使用默认配置
	
	// 尝试获取不存在的代理
	_, err := manager.GetProxyByID("non-existent")
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	}
	
	// 尝试从空列表获取随机代理
	_, err = manager.GetRandomProxy()
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	}
	
	// 尝试导入无效订阅
	ctx := context.Background()
	err = manager.ImportSubscription(ctx, "invalid-url", ProxyTypeShadowsocks)
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	}
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	fmt.Println("=== 代理管理系统示例 ===")
	
	ExampleHealthCheck()
	ExampleProxySelection()
	ExampleProxyFiltering()
	ExampleErrorHandling()
	
	// 注意：ExampleUsage() 可能需要实际的网络连接
	// ExampleUsage()
	
	fmt.Println()
	fmt.Println("=== 示例完成 ===")
}
