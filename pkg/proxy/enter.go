package proxy

import (
	"context"
	"time"
)

// ProxyType 代理类型枚举
type ProxyType string

const (
	ProxyTypeShadowsocks ProxyType = "shadowsocks"
	ProxyTypeSSR         ProxyType = "ssr"
	ProxyTypeVMess       ProxyType = "vmess"
	ProxyTypeTrojan      ProxyType = "trojan"
)

// ProxyStatus 代理状态
type ProxyStatus int

const (
	ProxyStatusUnknown ProxyStatus = iota
	ProxyStatusHealthy
	ProxyStatusUnhealthy
	ProxyStatusTimeout
)

// Proxy 代理接口，表示单个代理连接
type Proxy interface {
	// GetID 获取代理唯一标识
	GetID() string

	// GetType 获取代理类型
	GetType() ProxyType

	// GetAddress 获取代理地址
	GetAddress() string

	// GetPort 获取代理端口
	GetPort() int

	// GetConfig 获取代理配置信息
	GetConfig() map[string]interface{}

	// GetStatus 获取代理状态
	GetStatus() ProxyStatus

	// SetStatus 设置代理状态
	SetStatus(status ProxyStatus)

	// GetLastCheck 获取最后检查时间
	GetLastCheck() time.Time

	// IsHealthy 检查代理是否健康
	IsHealthy() bool

	// Clone 克隆代理实例
	Clone() Proxy
}

// ProxyProvider 代理提供者接口，用于从不同来源获取代理
type ProxyProvider interface {
	// GetName 获取提供者名称
	GetName() string

	// GetType 获取支持的代理类型
	GetType() ProxyType

	// ParseSubscription 解析订阅链接，返回代理列表
	ParseSubscription(ctx context.Context, subscriptionURL string) ([]Proxy, error)

	// ValidateProxy 验证代理配置是否有效
	ValidateProxy(proxy Proxy) error

	// CreateProxy 从配置创建代理实例
	CreateProxy(config map[string]interface{}) (Proxy, error)
}

// ProxyManager 代理管理器接口，核心管理接口
type ProxyManager interface {
	// AddProvider 添加代理提供者
	AddProvider(provider ProxyProvider) error

	// RemoveProvider 移除代理提供者
	RemoveProvider(providerName string) error

	// ImportSubscription 导入机场订阅链接
	ImportSubscription(ctx context.Context, subscriptionURL string, providerType ProxyType) error

	// AddProxy 手动添加代理
	AddProxy(proxy Proxy) error

	// GetRandomProxy 随机获取一个健康的代理
	GetRandomProxy() (Proxy, error)

	// GetProxyByID 根据ID获取代理
	GetProxyByID(id string) (Proxy, error)

	// GetProxiesByType 根据类型获取代理列表
	GetProxiesByType(proxyType ProxyType) []Proxy

	// GetAllProxies 获取所有代理
	GetAllProxies() []Proxy

	// GetHealthyProxies 获取所有健康的代理
	GetHealthyProxies() []Proxy

	// RemoveProxy 移除指定代理
	RemoveProxy(id string) error

	// ClearProxies 清空所有代理
	ClearProxies() error

	// GetProxyCount 获取代理总数
	GetProxyCount() int

	// GetHealthyProxyCount 获取健康代理数量
	GetHealthyProxyCount() int

	// StartHealthCheck 启动健康检查
	StartHealthCheck(ctx context.Context, interval time.Duration) error

	// StopHealthCheck 停止健康检查
	StopHealthCheck() error

	// CheckProxy 检查单个代理健康状态
	CheckProxy(ctx context.Context, proxy Proxy) error

	// SetProxyFilter 设置代理过滤器
	SetProxyFilter(filter ProxyFilter)

	// SetProxySelector 设置代理选择器
	SetProxySelector(selector ProxySelector)
}

// ProxyFilter 代理过滤器接口
type ProxyFilter interface {
	// Filter 过滤代理列表
	Filter(proxies []Proxy) []Proxy
}

// ProxySelector 代理选择器接口
type ProxySelector interface {
	// Select 从代理列表中选择一个代理
	Select(proxies []Proxy) (Proxy, error)
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	// Check 检查代理健康状态
	Check(ctx context.Context, proxy Proxy) error

	// GetTimeout 获取检查超时时间
	GetTimeout() time.Duration

	// SetTimeout 设置检查超时时间
	SetTimeout(timeout time.Duration)
}

// ProxyManagerConfig 代理管理器配置
type ProxyManagerConfig struct {
	// HealthCheckInterval 健康检查间隔
	HealthCheckInterval time.Duration

	// HealthCheckTimeout 健康检查超时时间
	HealthCheckTimeout time.Duration

	// MaxRetries 最大重试次数
	MaxRetries int

	// EnableHealthCheck 是否启用健康检查
	EnableHealthCheck bool

	// ProxyTimeout 代理连接超时时间
	ProxyTimeout time.Duration
}

// DefaultProxyManagerConfig 默认配置
func DefaultProxyManagerConfig() *ProxyManagerConfig {
	return &ProxyManagerConfig{
		HealthCheckInterval: 5 * time.Minute,
		HealthCheckTimeout:  10 * time.Second,
		MaxRetries:          3,
		EnableHealthCheck:   true,
		ProxyTimeout:        30 * time.Second,
	}
}

// NewProxyManager 创建新的代理管理器实例
func NewProxyManager(config *ProxyManagerConfig) ProxyManager {
	if config == nil {
		config = DefaultProxyManagerConfig()
	}
	return newProxyManager(config)
}
