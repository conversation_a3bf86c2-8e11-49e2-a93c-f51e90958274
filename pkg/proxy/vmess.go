package proxy

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// VMessProxy VMess 代理实现
type VMessProxy struct {
	id        string
	address   string
	port      int
	userID    string
	alterID   int
	network   string
	security  string
	path      string
	host      string
	tls       string
	status    ProxyStatus
	lastCheck time.Time
	createdAt time.Time
	updatedAt time.Time
}

// VMessConfig VMess 配置结构
type VMessConfig struct {
	Version  string `json:"v"`
	PS       string `json:"ps"`   // 备注名称
	Add      string `json:"add"`  // 服务器地址
	Port     string `json:"port"` // 端口
	ID       string `json:"id"`   // 用户ID
	AID      string `json:"aid"`  // 额外ID
	Net      string `json:"net"`  // 传输协议
	Type     string `json:"type"` // 伪装类型
	Host     string `json:"host"` // 伪装域名
	Path     string `json:"path"` // 路径
	TLS      string `json:"tls"`  // TLS
	Security string `json:"scy"`  // 加密方式
}

// NewVMessProxy 创建新的 VMess 代理
func NewVMessProxy(address string, port int, userID string, alterID int, network, security, path, host, tls string) *VMessProxy {
	now := time.Now()
	proxy := &VMessProxy{
		address:   address,
		port:      port,
		userID:    userID,
		alterID:   alterID,
		network:   network,
		security:  security,
		path:      path,
		host:      host,
		tls:       tls,
		status:    ProxyStatusUnknown,
		createdAt: now,
		updatedAt: now,
	}
	proxy.id = proxy.generateID()
	return proxy
}

// generateID 生成代理唯一ID
func (vp *VMessProxy) generateID() string {
	data := fmt.Sprintf("%s:%d:%s:%s", vp.address, vp.port, vp.userID, vp.network)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("vmess_%x", hash)[:16]
}

// GetID 获取代理唯一标识
func (vp *VMessProxy) GetID() string {
	return vp.id
}

// GetType 获取代理类型
func (vp *VMessProxy) GetType() ProxyType {
	return ProxyTypeVMess
}

// GetAddress 获取代理地址
func (vp *VMessProxy) GetAddress() string {
	return vp.address
}

// GetPort 获取代理端口
func (vp *VMessProxy) GetPort() int {
	return vp.port
}

// GetConfig 获取代理配置信息
func (vp *VMessProxy) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"address":  vp.address,
		"port":     vp.port,
		"user_id":  vp.userID,
		"alter_id": vp.alterID,
		"network":  vp.network,
		"security": vp.security,
		"path":     vp.path,
		"host":     vp.host,
		"tls":      vp.tls,
	}
}

// GetStatus 获取代理状态
func (vp *VMessProxy) GetStatus() ProxyStatus {
	return vp.status
}

// SetStatus 设置代理状态
func (vp *VMessProxy) SetStatus(status ProxyStatus) {
	vp.status = status
	vp.lastCheck = time.Now()
	vp.updatedAt = time.Now()
}

// GetLastCheck 获取最后检查时间
func (vp *VMessProxy) GetLastCheck() time.Time {
	return vp.lastCheck
}

// IsHealthy 检查代理是否健康
func (vp *VMessProxy) IsHealthy() bool {
	return vp.status == ProxyStatusHealthy
}

// Clone 克隆代理实例
func (vp *VMessProxy) Clone() Proxy {
	return &VMessProxy{
		id:        vp.id,
		address:   vp.address,
		port:      vp.port,
		userID:    vp.userID,
		alterID:   vp.alterID,
		network:   vp.network,
		security:  vp.security,
		path:      vp.path,
		host:      vp.host,
		tls:       vp.tls,
		status:    vp.status,
		lastCheck: vp.lastCheck,
		createdAt: vp.createdAt,
		updatedAt: vp.updatedAt,
	}
}

// VMessProvider VMess 代理提供者
type VMessProvider struct {
	name string
}

// NewVMessProvider 创建新的 VMess 提供者
func NewVMessProvider() *VMessProvider {
	return &VMessProvider{
		name: "vmess",
	}
}

// GetName 获取提供者名称
func (vp *VMessProvider) GetName() string {
	return vp.name
}

// GetType 获取支持的代理类型
func (vp *VMessProvider) GetType() ProxyType {
	return ProxyTypeVMess
}

// ParseSubscription VMess 提供者不直接解析订阅，由通用提供者处理
func (vp *VMessProvider) ParseSubscription(ctx context.Context, subscriptionURL string) ([]Proxy, error) {
	return nil, fmt.Errorf("VMess provider does not support direct subscription parsing")
}

// ValidateProxy 验证代理配置是否有效
func (vp *VMessProvider) ValidateProxy(proxy Proxy) error {
	if proxy == nil {
		return fmt.Errorf("proxy is nil")
	}

	if proxy.GetType() != ProxyTypeVMess {
		return fmt.Errorf("invalid proxy type: %s", proxy.GetType())
	}

	config := proxy.GetConfig()

	// 验证必需字段
	if config["address"] == "" {
		return fmt.Errorf("missing address")
	}

	if config["port"] == nil || config["port"].(int) <= 0 {
		return fmt.Errorf("invalid port")
	}

	if config["user_id"] == "" {
		return fmt.Errorf("missing user_id")
	}

	return nil
}

// CreateProxy 从配置创建代理实例
func (vp *VMessProvider) CreateProxy(config map[string]interface{}) (Proxy, error) {
	address, ok := config["address"].(string)
	if !ok || address == "" {
		return nil, fmt.Errorf("invalid address")
	}

	port, ok := config["port"].(int)
	if !ok || port <= 0 {
		return nil, fmt.Errorf("invalid port")
	}

	userID, ok := config["user_id"].(string)
	if !ok || userID == "" {
		return nil, fmt.Errorf("invalid user_id")
	}

	alterID, _ := config["alter_id"].(int)
	network, _ := config["network"].(string)
	security, _ := config["security"].(string)
	path, _ := config["path"].(string)
	host, _ := config["host"].(string)
	tls, _ := config["tls"].(string)

	return NewVMessProxy(address, port, userID, alterID, network, security, path, host, tls), nil
}

// ParseVMessURL 解析 VMess URL
func ParseVMessURL(vmessURL string) (Proxy, error) {
	if !strings.HasPrefix(vmessURL, "vmess://") {
		return nil, fmt.Errorf("invalid VMess URL format")
	}

	// 移除 vmess:// 前缀
	encoded := vmessURL[8:]

	// Base64 解码
	decoded, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		// 尝试 URL 安全的 Base64 解码
		decoded, err = base64.URLEncoding.DecodeString(encoded)
		if err != nil {
			return nil, fmt.Errorf("failed to decode VMess URL: %w", err)
		}
	}

	// 解析 JSON 配置
	var config VMessConfig
	if err := json.Unmarshal(decoded, &config); err != nil {
		return nil, fmt.Errorf("failed to parse VMess config: %w", err)
	}

	// 转换端口
	port, err := strconv.Atoi(config.Port)
	if err != nil {
		return nil, fmt.Errorf("invalid port: %s", config.Port)
	}

	// 转换 alterID
	alterID, _ := strconv.Atoi(config.AID)

	// 设置默认值
	network := config.Net
	if network == "" {
		network = "tcp"
	}

	security := config.Security
	if security == "" {
		security = "auto"
	}

	return NewVMessProxy(
		config.Add,
		port,
		config.ID,
		alterID,
		network,
		security,
		config.Path,
		config.Host,
		config.TLS,
	), nil
}
