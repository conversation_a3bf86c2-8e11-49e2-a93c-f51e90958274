package proxy

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// ShadowsocksProxy Shadowsocks 代理实现
type ShadowsocksProxy struct {
	id        string
	address   string
	port      int
	password  string
	method    string
	status    ProxyStatus
	lastCheck time.Time
	createdAt time.Time
	updatedAt time.Time
}

// NewShadowsocksProxy 创建新的 Shadowsocks 代理
func NewShadowsocksProxy(address string, port int, password, method string) *ShadowsocksProxy {
	now := time.Now()
	proxy := &ShadowsocksProxy{
		address:   address,
		port:      port,
		password:  password,
		method:    method,
		status:    ProxyStatusUnknown,
		createdAt: now,
		updatedAt: now,
	}
	proxy.id = proxy.generateID()
	return proxy
}

// generateID 生成代理唯一ID
func (sp *ShadowsocksProxy) generateID() string {
	data := fmt.Sprintf("%s:%d:%s:%s", sp.address, sp.port, sp.password, sp.method)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("ss_%x", hash)[:16]
}

// GetID 获取代理唯一标识
func (sp *ShadowsocksProxy) GetID() string {
	return sp.id
}

// GetType 获取代理类型
func (sp *ShadowsocksProxy) GetType() ProxyType {
	return ProxyTypeShadowsocks
}

// GetAddress 获取代理地址
func (sp *ShadowsocksProxy) GetAddress() string {
	return sp.address
}

// GetPort 获取代理端口
func (sp *ShadowsocksProxy) GetPort() int {
	return sp.port
}

// GetConfig 获取代理配置信息
func (sp *ShadowsocksProxy) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"address":  sp.address,
		"port":     sp.port,
		"password": sp.password,
		"method":   sp.method,
	}
}

// GetStatus 获取代理状态
func (sp *ShadowsocksProxy) GetStatus() ProxyStatus {
	return sp.status
}

// SetStatus 设置代理状态
func (sp *ShadowsocksProxy) SetStatus(status ProxyStatus) {
	sp.status = status
	sp.lastCheck = time.Now()
	sp.updatedAt = time.Now()
}

// GetLastCheck 获取最后检查时间
func (sp *ShadowsocksProxy) GetLastCheck() time.Time {
	return sp.lastCheck
}

// IsHealthy 检查代理是否健康
func (sp *ShadowsocksProxy) IsHealthy() bool {
	return sp.status == ProxyStatusHealthy
}

// Clone 克隆代理实例
func (sp *ShadowsocksProxy) Clone() Proxy {
	return &ShadowsocksProxy{
		id:        sp.id,
		address:   sp.address,
		port:      sp.port,
		password:  sp.password,
		method:    sp.method,
		status:    sp.status,
		lastCheck: sp.lastCheck,
		createdAt: sp.createdAt,
		updatedAt: sp.updatedAt,
	}
}

// ShadowsocksProvider Shadowsocks 代理提供者
type ShadowsocksProvider struct {
	name string
}

// NewShadowsocksProvider 创建新的 Shadowsocks 提供者
func NewShadowsocksProvider() *ShadowsocksProvider {
	return &ShadowsocksProvider{
		name: "shadowsocks",
	}
}

// GetName 获取提供者名称
func (sp *ShadowsocksProvider) GetName() string {
	return sp.name
}

// GetType 获取支持的代理类型
func (sp *ShadowsocksProvider) GetType() ProxyType {
	return ProxyTypeShadowsocks
}

// ParseSubscription 解析订阅链接，返回代理列表
func (sp *ShadowsocksProvider) ParseSubscription(ctx context.Context, subscriptionURL string) ([]Proxy, error) {
	// 验证 URL
	if _, err := url.Parse(subscriptionURL); err != nil {
		return nil, fmt.Errorf("invalid subscription URL: %w", err)
	}
	
	// 创建 HTTP 客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", subscriptionURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置 User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch subscription: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("subscription request failed with status: %d", resp.StatusCode)
	}
	
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	// 尝试 Base64 解码
	decoded, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		// 如果解码失败，直接使用原始内容
		decoded = body
	}
	
	// 解析代理配置
	return sp.parseProxyConfigs(string(decoded))
}

// parseProxyConfigs 解析代理配置
func (sp *ShadowsocksProvider) parseProxyConfigs(content string) ([]Proxy, error) {
	var proxies []Proxy
	
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		// 尝试解析不同格式的 SS 链接
		proxy, err := sp.parseSSURL(line)
		if err != nil {
			// 尝试解析 JSON 格式
			proxy, err = sp.parseSSJSON(line)
			if err != nil {
				continue // 跳过无法解析的行
			}
		}
		
		if proxy != nil {
			proxies = append(proxies, proxy)
		}
	}
	
	return proxies, nil
}

// parseSSURL 解析 SS URL 格式 (ss://method:password@server:port)
func (sp *ShadowsocksProvider) parseSSURL(ssURL string) (Proxy, error) {
	if !strings.HasPrefix(ssURL, "ss://") {
		return nil, fmt.Errorf("invalid SS URL format")
	}
	
	// 移除 ss:// 前缀
	ssURL = ssURL[5:]
	
	// 尝试 Base64 解码
	decoded, err := base64.URLEncoding.DecodeString(ssURL)
	if err != nil {
		decoded, err = base64.StdEncoding.DecodeString(ssURL)
		if err != nil {
			// 如果不是 Base64 编码，直接解析
			decoded = []byte(ssURL)
		}
	}
	
	// 解析格式: method:password@server:port
	parts := strings.Split(string(decoded), "@")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid SS URL format")
	}
	
	// 解析认证信息
	authParts := strings.Split(parts[0], ":")
	if len(authParts) != 2 {
		return nil, fmt.Errorf("invalid auth format")
	}
	method := authParts[0]
	password := authParts[1]
	
	// 解析服务器信息
	serverParts := strings.Split(parts[1], ":")
	if len(serverParts) != 2 {
		return nil, fmt.Errorf("invalid server format")
	}
	address := serverParts[0]
	port, err := strconv.Atoi(serverParts[1])
	if err != nil {
		return nil, fmt.Errorf("invalid port: %w", err)
	}
	
	return NewShadowsocksProxy(address, port, password, method), nil
}

// parseSSJSON 解析 JSON 格式的 SS 配置
func (sp *ShadowsocksProvider) parseSSJSON(jsonStr string) (Proxy, error) {
	var config struct {
		Server     string `json:"server"`
		ServerPort int    `json:"server_port"`
		Password   string `json:"password"`
		Method     string `json:"method"`
	}
	
	if err := json.Unmarshal([]byte(jsonStr), &config); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	if config.Server == "" || config.ServerPort == 0 || config.Password == "" || config.Method == "" {
		return nil, fmt.Errorf("incomplete SS configuration")
	}
	
	return NewShadowsocksProxy(config.Server, config.ServerPort, config.Password, config.Method), nil
}

// ValidateProxy 验证代理配置是否有效
func (sp *ShadowsocksProvider) ValidateProxy(proxy Proxy) error {
	if proxy == nil {
		return fmt.Errorf("proxy is nil")
	}
	
	if proxy.GetType() != ProxyTypeShadowsocks {
		return fmt.Errorf("invalid proxy type: %s", proxy.GetType())
	}
	
	config := proxy.GetConfig()
	
	// 验证必需字段
	if config["address"] == "" {
		return fmt.Errorf("missing address")
	}
	
	if config["port"] == nil || config["port"].(int) <= 0 {
		return fmt.Errorf("invalid port")
	}
	
	if config["password"] == "" {
		return fmt.Errorf("missing password")
	}
	
	if config["method"] == "" {
		return fmt.Errorf("missing method")
	}
	
	return nil
}

// CreateProxy 从配置创建代理实例
func (sp *ShadowsocksProvider) CreateProxy(config map[string]interface{}) (Proxy, error) {
	address, ok := config["address"].(string)
	if !ok || address == "" {
		return nil, fmt.Errorf("invalid address")
	}
	
	port, ok := config["port"].(int)
	if !ok || port <= 0 {
		return nil, fmt.Errorf("invalid port")
	}
	
	password, ok := config["password"].(string)
	if !ok || password == "" {
		return nil, fmt.Errorf("invalid password")
	}
	
	method, ok := config["method"].(string)
	if !ok || method == "" {
		return nil, fmt.Errorf("invalid method")
	}
	
	return NewShadowsocksProxy(address, port, password, method), nil
}
