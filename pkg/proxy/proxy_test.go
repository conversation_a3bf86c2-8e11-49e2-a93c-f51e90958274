package proxy

import (
	"context"
	"testing"
	"time"
)

// TestShadowsocksProxy 测试 Shadowsocks 代理
func TestShadowsocksProxy(t *testing.T) {
	proxy := NewShadowsocksProxy("127.0.0.1", 8388, "password", "aes-256-gcm")
	
	// 测试基本属性
	if proxy.GetType() != ProxyTypeShadowsocks {
		t.<PERSON><PERSON>("Expected type %s, got %s", ProxyTypeShadowsocks, proxy.GetType())
	}
	
	if proxy.GetAddress() != "127.0.0.1" {
		t.<PERSON><PERSON><PERSON>("Expected address 127.0.0.1, got %s", proxy.GetAddress())
	}
	
	if proxy.GetPort() != 8388 {
		t.<PERSON><PERSON>("Expected port 8388, got %d", proxy.GetPort())
	}
	
	// 测试状态管理
	if proxy.GetStatus() != ProxyStatusUnknown {
		t.<PERSON><PERSON>rf("Expected initial status %s, got %s", ProxyStatusUnknown, proxy.GetStatus())
	}
	
	proxy.SetStatus(ProxyStatusHealthy)
	if proxy.GetStatus() != ProxyStatusHealthy {
		t.<PERSON>("Expected status %s, got %s", ProxyStatusHealthy, proxy.GetStatus())
	}
	
	if !proxy.IsHealthy() {
		t.Error("Expected proxy to be healthy")
	}
	
	// 测试配置
	config := proxy.GetConfig()
	if config["address"] != "127.0.0.1" {
		t.Errorf("Expected config address 127.0.0.1, got %v", config["address"])
	}
	
	// 测试克隆
	cloned := proxy.Clone()
	if cloned.GetID() != proxy.GetID() {
		t.Error("Cloned proxy should have same ID")
	}
	
	// 修改克隆不应影响原始代理
	cloned.SetStatus(ProxyStatusUnhealthy)
	if proxy.GetStatus() == ProxyStatusUnhealthy {
		t.Error("Modifying cloned proxy should not affect original")
	}
}

// TestShadowsocksProvider 测试 Shadowsocks 提供者
func TestShadowsocksProvider(t *testing.T) {
	provider := NewShadowsocksProvider()
	
	if provider.GetName() != "shadowsocks" {
		t.Errorf("Expected name shadowsocks, got %s", provider.GetName())
	}
	
	if provider.GetType() != ProxyTypeShadowsocks {
		t.Errorf("Expected type %s, got %s", ProxyTypeShadowsocks, provider.GetType())
	}
	
	// 测试代理创建
	config := map[string]interface{}{
		"address":  "127.0.0.1",
		"port":     8388,
		"password": "test",
		"method":   "aes-256-gcm",
	}
	
	proxy, err := provider.CreateProxy(config)
	if err != nil {
		t.Fatalf("Failed to create proxy: %v", err)
	}
	
	if proxy.GetAddress() != "127.0.0.1" {
		t.Errorf("Expected address 127.0.0.1, got %s", proxy.GetAddress())
	}
	
	// 测试代理验证
	if err := provider.ValidateProxy(proxy); err != nil {
		t.Errorf("Proxy validation failed: %v", err)
	}
	
	// 测试无效配置
	invalidConfig := map[string]interface{}{
		"address": "",
		"port":    0,
	}
	
	_, err = provider.CreateProxy(invalidConfig)
	if err == nil {
		t.Error("Expected error for invalid config")
	}
}

// TestProxyManager 测试代理管理器
func TestProxyManager(t *testing.T) {
	config := DefaultProxyManagerConfig()
	manager := NewProxyManager(config)
	
	// 测试添加提供者
	provider := NewShadowsocksProvider()
	if err := manager.AddProvider(provider); err != nil {
		t.Fatalf("Failed to add provider: %v", err)
	}
	
	// 测试重复添加提供者
	if err := manager.AddProvider(provider); err != ErrProviderExists {
		t.Errorf("Expected ErrProviderExists, got %v", err)
	}
	
	// 测试代理计数
	if count := manager.GetProxyCount(); count != 0 {
		t.Errorf("Expected 0 proxies, got %d", count)
	}
	
	// 测试获取不存在的代理
	_, err := manager.GetProxyByID("non-existent")
	if err != ErrProxyNotFound {
		t.Errorf("Expected ErrProxyNotFound, got %v", err)
	}
	
	// 测试从空列表获取随机代理
	_, err = manager.GetRandomProxy()
	if err != ErrNoHealthyProxy {
		t.Errorf("Expected ErrNoHealthyProxy, got %v", err)
	}
	
	// 测试移除提供者
	if err := manager.RemoveProvider("shadowsocks"); err != nil {
		t.Errorf("Failed to remove provider: %v", err)
	}
	
	// 测试移除不存在的提供者
	if err := manager.RemoveProvider("non-existent"); err != ErrProviderNotFound {
		t.Errorf("Expected ErrProviderNotFound, got %v", err)
	}

	// 测试手动添加代理
	testProxy := NewShadowsocksProxy("127.0.0.1", 8388, "test", "aes-256-gcm")
	if err := manager.AddProxy(testProxy); err != nil {
		t.Errorf("Failed to add proxy: %v", err)
	}

	if count := manager.GetProxyCount(); count != 1 {
		t.Errorf("Expected 1 proxy after adding, got %d", count)
	}

	// 测试添加重复代理
	if err := manager.AddProxy(testProxy); err == nil {
		t.Error("Expected error when adding duplicate proxy")
	}

	// 测试添加 nil 代理
	if err := manager.AddProxy(nil); err == nil {
		t.Error("Expected error when adding nil proxy")
	}
}

// TestProxyTypes 测试代理类型
func TestProxyTypes(t *testing.T) {
	// 测试有效类型
	validTypes := []ProxyType{
		ProxyTypeShadowsocks,
		ProxyTypeSSR,
		ProxyTypeVMess,
		ProxyTypeTrojan,
	}
	
	for _, pt := range validTypes {
		if !pt.IsValid() {
			t.Errorf("Type %s should be valid", pt)
		}
	}
	
	// 测试无效类型
	invalidType := ProxyType("invalid")
	if invalidType.IsValid() {
		t.Error("Invalid type should not be valid")
	}
	
	// 测试类型解析
	parsed, err := ParseProxyType("shadowsocks")
	if err != nil {
		t.Errorf("Failed to parse valid type: %v", err)
	}
	if parsed != ProxyTypeShadowsocks {
		t.Errorf("Expected %s, got %s", ProxyTypeShadowsocks, parsed)
	}
	
	_, err = ParseProxyType("invalid")
	if err != ErrInvalidProxyType {
		t.Errorf("Expected ErrInvalidProxyType, got %v", err)
	}
}

// TestProxyStatus 测试代理状态
func TestProxyStatus(t *testing.T) {
	statuses := []struct {
		status   ProxyStatus
		expected string
		healthy  bool
	}{
		{ProxyStatusHealthy, "healthy", true},
		{ProxyStatusUnhealthy, "unhealthy", false},
		{ProxyStatusTimeout, "timeout", false},
		{ProxyStatusUnknown, "unknown", false},
	}
	
	for _, s := range statuses {
		if s.status.String() != s.expected {
			t.Errorf("Expected status string %s, got %s", s.expected, s.status.String())
		}
		
		if s.status.IsHealthy() != s.healthy {
			t.Errorf("Expected healthy status %v for %s", s.healthy, s.status.String())
		}
	}
}

// TestHealthChecker 测试健康检查器
func TestHealthChecker(t *testing.T) {
	checker := NewDefaultHealthChecker(5 * time.Second)
	
	if checker.GetTimeout() != 5*time.Second {
		t.Errorf("Expected timeout 5s, got %v", checker.GetTimeout())
	}
	
	// 设置新的超时时间
	checker.SetTimeout(10 * time.Second)
	if checker.GetTimeout() != 10*time.Second {
		t.Errorf("Expected timeout 10s, got %v", checker.GetTimeout())
	}
	
	// 测试检查 nil 代理
	ctx := context.Background()
	err := checker.Check(ctx, nil)
	if err == nil {
		t.Error("Expected error for nil proxy")
	}
}

// TestProxySelectors 测试代理选择器
func TestProxySelectors(t *testing.T) {
	// 创建测试代理
	proxies := []Proxy{
		NewShadowsocksProxy("*******", 8388, "pass1", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8388, "pass2", "aes-256-gcm"),
		NewShadowsocksProxy("3.3.3.3", 8388, "pass3", "aes-256-gcm"),
	}
	
	// 设置为健康状态
	for _, proxy := range proxies {
		proxy.SetStatus(ProxyStatusHealthy)
	}
	
	// 测试随机选择器
	randomSelector := NewRandomSelector()
	proxy, err := randomSelector.Select(proxies)
	if err != nil {
		t.Errorf("Random selector failed: %v", err)
	}
	if proxy == nil {
		t.Error("Random selector returned nil proxy")
	}
	
	// 测试空列表
	_, err = randomSelector.Select([]Proxy{})
	if err != ErrNoHealthyProxy {
		t.Errorf("Expected ErrNoHealthyProxy, got %v", err)
	}
	
	// 测试轮询选择器
	rrSelector := NewRoundRobinSelector()
	selectedAddresses := make(map[string]bool)
	
	for i := 0; i < len(proxies)*2; i++ {
		proxy, err := rrSelector.Select(proxies)
		if err != nil {
			t.Errorf("Round robin selector failed: %v", err)
		}
		selectedAddresses[proxy.GetAddress()] = true
	}
	
	// 应该选择了所有代理
	if len(selectedAddresses) != len(proxies) {
		t.Errorf("Expected %d unique addresses, got %d", len(proxies), len(selectedAddresses))
	}
}

// TestProxyFilters 测试代理过滤器
func TestProxyFilters(t *testing.T) {
	// 创建测试代理
	proxies := []Proxy{
		NewShadowsocksProxy("*******", 8388, "pass1", "aes-256-gcm"),
		NewShadowsocksProxy("*******", 8388, "pass2", "aes-256-gcm"),
	}
	
	// 设置不同状态
	proxies[0].SetStatus(ProxyStatusHealthy)
	proxies[1].SetStatus(ProxyStatusUnhealthy)
	
	// 测试健康过滤器
	healthyFilter := NewHealthyFilter()
	filtered := healthyFilter.Filter(proxies)
	
	if len(filtered) != 1 {
		t.Errorf("Expected 1 healthy proxy, got %d", len(filtered))
	}
	
	if !filtered[0].IsHealthy() {
		t.Error("Filtered proxy should be healthy")
	}
	
	// 测试类型过滤器
	typeFilter := NewTypeFilter(ProxyTypeShadowsocks)
	filtered = typeFilter.Filter(proxies)
	
	if len(filtered) != 2 {
		t.Errorf("Expected 2 SS proxies, got %d", len(filtered))
	}
	
	// 测试空类型过滤器（应该返回所有代理）
	emptyTypeFilter := NewTypeFilter()
	filtered = emptyTypeFilter.Filter(proxies)
	
	if len(filtered) != len(proxies) {
		t.Errorf("Expected %d proxies, got %d", len(proxies), len(filtered))
	}
}

// TestProxyError 测试代理错误
func TestProxyError(t *testing.T) {
	baseErr := ErrProxyNotFound
	proxyErr := NewProxyError("TEST", "test message", baseErr)
	
	expectedMsg := "TEST: test message (caused by: proxy not found)"
	if proxyErr.Error() != expectedMsg {
		t.Errorf("Expected error message %s, got %s", expectedMsg, proxyErr.Error())
	}
	
	if proxyErr.Unwrap() != baseErr {
		t.Error("Unwrap should return the base error")
	}
	
	// 测试没有 cause 的错误
	simpleErr := NewProxyError("SIMPLE", "simple message", nil)
	expectedSimpleMsg := "SIMPLE: simple message"
	if simpleErr.Error() != expectedSimpleMsg {
		t.Errorf("Expected error message %s, got %s", expectedSimpleMsg, simpleErr.Error())
	}
}
