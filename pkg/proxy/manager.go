package proxy

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"
)

// proxyManager 代理管理器实现
type proxyManager struct {
	config    *ProxyManagerConfig
	providers map[string]ProxyProvider
	proxies   map[string]Proxy
	filter    ProxyFilter
	selector  ProxySelector
	checker   HealthChecker
	
	// 健康检查相关
	healthCheckCtx    context.Context
	healthCheckCancel context.CancelFunc
	healthCheckDone   chan struct{}
	
	// 线程安全
	mu sync.RWMutex
	
	// 统计信息
	stats ProxyStats
}

// newProxyManager 创建新的代理管理器
func newProxyManager(config *ProxyManagerConfig) ProxyManager {
	return &proxyManager{
		config:    config,
		providers: make(map[string]ProxyProvider),
		proxies:   make(map[string]Proxy),
		stats: ProxyStats{
			ProviderStats: make(map[string]int),
		},
	}
}

// AddProvider 添加代理提供者
func (pm *proxyManager) AddProvider(provider ProxyProvider) error {
	if provider == nil {
		return errors.New("provider cannot be nil")
	}
	
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	name := provider.GetName()
	if _, exists := pm.providers[name]; exists {
		return ErrProviderExists
	}
	
	pm.providers[name] = provider
	pm.stats.ProviderStats[name] = 0
	
	return nil
}

// RemoveProvider 移除代理提供者
func (pm *proxyManager) RemoveProvider(providerName string) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if _, exists := pm.providers[providerName]; !exists {
		return ErrProviderNotFound
	}
	
	delete(pm.providers, providerName)
	delete(pm.stats.ProviderStats, providerName)
	
	return nil
}

// ImportSubscription 导入机场订阅链接
func (pm *proxyManager) ImportSubscription(ctx context.Context, subscriptionURL string, providerType ProxyType) error {
	if subscriptionURL == "" {
		return ErrInvalidSubscription
	}
	
	if !providerType.IsValid() {
		return ErrInvalidProxyType
	}
	
	pm.mu.RLock()
	var provider ProxyProvider
	for _, p := range pm.providers {
		if p.GetType() == providerType {
			provider = p
			break
		}
	}
	pm.mu.RUnlock()
	
	if provider == nil {
		return fmt.Errorf("no provider found for type: %s", providerType)
	}
	
	proxies, err := provider.ParseSubscription(ctx, subscriptionURL)
	if err != nil {
		return fmt.Errorf("failed to parse subscription: %w", err)
	}
	
	if len(proxies) == 0 {
		return ErrEmptyProxyList
	}
	
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	providerName := provider.GetName()
	addedCount := 0
	
	for _, proxy := range proxies {
		if err := provider.ValidateProxy(proxy); err != nil {
			continue // 跳过无效的代理
		}
		
		pm.proxies[proxy.GetID()] = proxy
		addedCount++
	}
	
	pm.stats.ProviderStats[providerName] += addedCount
	pm.updateStats()
	
	return nil
}

// AddProxy 手动添加代理
func (pm *proxyManager) AddProxy(proxy Proxy) error {
	if proxy == nil {
		return errors.New("proxy cannot be nil")
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 检查代理是否已存在
	if _, exists := pm.proxies[proxy.GetID()]; exists {
		return fmt.Errorf("proxy with ID %s already exists", proxy.GetID())
	}

	pm.proxies[proxy.GetID()] = proxy
	pm.updateStats()

	return nil
}

// GetRandomProxy 随机获取一个健康的代理
func (pm *proxyManager) GetRandomProxy() (Proxy, error) {
	healthyProxies := pm.GetHealthyProxies()
	if len(healthyProxies) == 0 {
		return nil, ErrNoHealthyProxy
	}
	
	// 如果设置了选择器，使用选择器
	if pm.selector != nil {
		return pm.selector.Select(healthyProxies)
	}
	
	// 默认随机选择
	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(healthyProxies))))
	if err != nil {
		return nil, fmt.Errorf("failed to generate random number: %w", err)
	}
	
	return healthyProxies[n.Int64()].Clone(), nil
}

// GetProxyByID 根据ID获取代理
func (pm *proxyManager) GetProxyByID(id string) (Proxy, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	proxy, exists := pm.proxies[id]
	if !exists {
		return nil, ErrProxyNotFound
	}
	
	return proxy.Clone(), nil
}

// GetProxiesByType 根据类型获取代理列表
func (pm *proxyManager) GetProxiesByType(proxyType ProxyType) []Proxy {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	var result []Proxy
	for _, proxy := range pm.proxies {
		if proxy.GetType() == proxyType {
			result = append(result, proxy.Clone())
		}
	}
	
	// 应用过滤器
	if pm.filter != nil {
		result = pm.filter.Filter(result)
	}
	
	return result
}

// GetAllProxies 获取所有代理
func (pm *proxyManager) GetAllProxies() []Proxy {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	result := make([]Proxy, 0, len(pm.proxies))
	for _, proxy := range pm.proxies {
		result = append(result, proxy.Clone())
	}
	
	// 应用过滤器
	if pm.filter != nil {
		result = pm.filter.Filter(result)
	}
	
	return result
}

// GetHealthyProxies 获取所有健康的代理
func (pm *proxyManager) GetHealthyProxies() []Proxy {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	var result []Proxy
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy() {
			result = append(result, proxy.Clone())
		}
	}
	
	// 应用过滤器
	if pm.filter != nil {
		result = pm.filter.Filter(result)
	}
	
	return result
}

// RemoveProxy 移除指定代理
func (pm *proxyManager) RemoveProxy(id string) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if _, exists := pm.proxies[id]; !exists {
		return ErrProxyNotFound
	}
	
	delete(pm.proxies, id)
	pm.updateStats()
	
	return nil
}

// ClearProxies 清空所有代理
func (pm *proxyManager) ClearProxies() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.proxies = make(map[string]Proxy)
	for provider := range pm.stats.ProviderStats {
		pm.stats.ProviderStats[provider] = 0
	}
	pm.updateStats()
	
	return nil
}

// GetProxyCount 获取代理总数
func (pm *proxyManager) GetProxyCount() int {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return len(pm.proxies)
}

// GetHealthyProxyCount 获取健康代理数量
func (pm *proxyManager) GetHealthyProxyCount() int {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	count := 0
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy() {
			count++
		}
	}
	return count
}

// updateStats 更新统计信息（需要在锁内调用）
func (pm *proxyManager) updateStats() {
	pm.stats.TotalProxies = len(pm.proxies)
	pm.stats.HealthyProxies = 0
	pm.stats.UnhealthyProxies = 0
	pm.stats.LastUpdate = time.Now()
	
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy() {
			pm.stats.HealthyProxies++
		} else {
			pm.stats.UnhealthyProxies++
		}
	}
}

// SetProxyFilter 设置代理过滤器
func (pm *proxyManager) SetProxyFilter(filter ProxyFilter) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.filter = filter
}

// SetProxySelector 设置代理选择器
func (pm *proxyManager) SetProxySelector(selector ProxySelector) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.selector = selector
}

// StartHealthCheck 启动健康检查
func (pm *proxyManager) StartHealthCheck(ctx context.Context, interval time.Duration) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.healthCheckCancel != nil {
		return errors.New("health check is already running")
	}

	if !pm.config.EnableHealthCheck {
		return errors.New("health check is disabled in config")
	}

	if pm.checker == nil {
		pm.checker = NewDefaultHealthChecker(pm.config.HealthCheckTimeout)
	}

	pm.healthCheckCtx, pm.healthCheckCancel = context.WithCancel(ctx)
	pm.healthCheckDone = make(chan struct{})

	go pm.runHealthCheck(interval)

	return nil
}

// StopHealthCheck 停止健康检查
func (pm *proxyManager) StopHealthCheck() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.healthCheckCancel == nil {
		return errors.New("health check is not running")
	}

	pm.healthCheckCancel()

	// 等待健康检查协程结束
	select {
	case <-pm.healthCheckDone:
	case <-time.After(5 * time.Second):
		// 超时，强制结束
	}

	pm.healthCheckCancel = nil
	pm.healthCheckDone = nil

	return nil
}

// CheckProxy 检查单个代理健康状态
func (pm *proxyManager) CheckProxy(ctx context.Context, proxy Proxy) error {
	if pm.checker == nil {
		pm.checker = NewDefaultHealthChecker(pm.config.HealthCheckTimeout)
	}

	return pm.checker.Check(ctx, proxy)
}

// runHealthCheck 运行健康检查循环
func (pm *proxyManager) runHealthCheck(interval time.Duration) {
	defer close(pm.healthCheckDone)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.healthCheckCtx.Done():
			return
		case <-ticker.C:
			pm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (pm *proxyManager) performHealthCheck() {
	pm.mu.RLock()
	proxies := make([]Proxy, 0, len(pm.proxies))
	for _, proxy := range pm.proxies {
		proxies = append(proxies, proxy)
	}
	pm.mu.RUnlock()

	// 并发检查所有代理
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 10) // 限制并发数

	for _, proxy := range proxies {
		wg.Add(1)
		go func(p Proxy) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			ctx, cancel := context.WithTimeout(pm.healthCheckCtx, pm.config.HealthCheckTimeout)
			defer cancel()

			err := pm.CheckProxy(ctx, p)
			if err != nil {
				p.SetStatus(ProxyStatusUnhealthy)
			} else {
				p.SetStatus(ProxyStatusHealthy)
			}
		}(proxy)
	}

	wg.Wait()

	// 更新统计信息
	pm.mu.Lock()
	pm.updateStats()
	pm.mu.Unlock()
}
