package proxy

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// UniversalProvider 通用代理提供者，支持解析混合订阅
type UniversalProvider struct {
	name string
}

// NewUniversalProvider 创建新的通用提供者
func NewUniversalProvider() *UniversalProvider {
	return &UniversalProvider{
		name: "universal",
	}
}

// GetName 获取提供者名称
func (up *UniversalProvider) GetName() string {
	return up.name
}

// GetType 获取支持的代理类型（通用提供者支持所有类型）
func (up *UniversalProvider) GetType() ProxyType {
	return ProxyType("universal")
}

// ParseSubscription 解析订阅链接，返回混合代理列表
func (up *UniversalProvider) ParseSubscription(ctx context.Context, subscriptionURL string) ([]Proxy, error) {
	// 验证 URL
	if _, err := url.Parse(subscriptionURL); err != nil {
		return nil, fmt.Errorf("invalid subscription URL: %w", err)
	}
	
	// 创建 HTTP 客户端，添加更多配置
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			DisableKeepAlives: true,
		},
	}
	
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", subscriptionURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置更完整的请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	
	// 发送请求，添加重试机制
	var resp *http.Response
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		resp, err = client.Do(req)
		if err == nil {
			break
		}
		
		if i < maxRetries-1 {
			time.Sleep(time.Duration(i+1) * time.Second)
			continue
		}
		return nil, fmt.Errorf("failed to fetch subscription after %d retries: %w", maxRetries, err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("subscription request failed with status: %d %s", resp.StatusCode, resp.Status)
	}
	
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	// 处理订阅内容
	processedContent, err := up.processSubscriptionContent(body)
	if err != nil {
		return nil, fmt.Errorf("failed to process subscription content: %w", err)
	}

	// 解析代理配置
	return up.parseProxyConfigs(processedContent)
}

// processSubscriptionContent 处理订阅内容，支持多种编码和压缩格式
func (up *UniversalProvider) processSubscriptionContent(body []byte) (string, error) {
	fmt.Printf("订阅原始内容长度: %d 字节\n", len(body))

	// 检查是否是二进制数据（可能是 gzip 压缩）
	if len(body) >= 2 && body[0] == 0x1f && body[1] == 0x8b {
		fmt.Println("检测到 gzip 压缩数据，尝试解压...")

		reader, err := gzip.NewReader(bytes.NewReader(body))
		if err != nil {
			return "", fmt.Errorf("failed to create gzip reader: %w", err)
		}
		defer reader.Close()

		decompressed, err := io.ReadAll(reader)
		if err != nil {
			return "", fmt.Errorf("failed to decompress gzip data: %w", err)
		}

		fmt.Printf("gzip 解压成功，解压后长度: %d 字节\n", len(decompressed))
		body = decompressed
	}

	// 显示内容预览
	if len(body) > 0 {
		preview := string(body)
		if len(preview) > 100 {
			preview = preview[:100] + "..."
		}
		fmt.Printf("内容预览: %s\n", preview)
	}

	// 检查是否是 Base64 编码
	content := string(body)

	// 如果内容看起来像 Base64（只包含 Base64 字符），尝试解码
	if up.isBase64Like(content) {
		fmt.Println("内容看起来像 Base64，尝试解码...")

		decoded, err := base64.StdEncoding.DecodeString(content)
		if err != nil {
			// 尝试 URL 安全的 Base64
			decoded, err = base64.URLEncoding.DecodeString(content)
			if err != nil {
				fmt.Printf("Base64 解码失败: %v，使用原始内容\n", err)
				return content, nil
			}
		}

		fmt.Printf("Base64 解码成功，解码后长度: %d 字节\n", len(decoded))
		decodedContent := string(decoded)

		// 显示解码后的内容预览
		if len(decodedContent) > 200 {
			fmt.Printf("解码后内容预览: %s\n", decodedContent[:200]+"...")
		} else {
			fmt.Printf("解码后内容: %s\n", decodedContent)
		}

		return decodedContent, nil
	}

	fmt.Println("使用原始内容")
	return content, nil
}

// isBase64Like 检查字符串是否看起来像 Base64
func (up *UniversalProvider) isBase64Like(s string) bool {
	if len(s) == 0 {
		return false
	}

	// 移除空白字符
	s = strings.ReplaceAll(s, " ", "")
	s = strings.ReplaceAll(s, "\n", "")
	s = strings.ReplaceAll(s, "\r", "")
	s = strings.ReplaceAll(s, "\t", "")

	// Base64 字符集
	base64Chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="

	// 检查是否只包含 Base64 字符
	for _, char := range s {
		if !strings.ContainsRune(base64Chars, char) {
			return false
		}
	}

	// 长度应该是 4 的倍数（忽略填充）
	cleanLength := len(strings.TrimRight(s, "="))
	return len(s)%4 == 0 || (len(s)-cleanLength) <= 2
}

// parseProxyConfigs 解析混合代理配置
func (up *UniversalProvider) parseProxyConfigs(content string) ([]Proxy, error) {
	var proxies []Proxy
	
	lines := strings.Split(content, "\n")
	fmt.Printf("开始解析 %d 行配置\n", len(lines))
	
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		fmt.Printf("解析第 %d 行: %s\n", i+1, line[:min(50, len(line))]+"...")
		
		var proxy Proxy
		var err error
		
		// 检测代理类型并解析
		if strings.HasPrefix(line, "vmess://") {
			fmt.Printf("  检测到 VMess 代理\n")
			proxy, err = ParseVMessURL(line)
			if err != nil {
				fmt.Printf("  VMess 解析失败: %v\n", err)
				continue
			}
		} else if strings.HasPrefix(line, "ss://") {
			fmt.Printf("  检测到 Shadowsocks 代理\n")
			proxy, err = up.parseSSURL(line)
			if err != nil {
				fmt.Printf("  Shadowsocks 解析失败: %v\n", err)
				continue
			}
		} else if strings.HasPrefix(line, "ssr://") {
			fmt.Printf("  检测到 SSR 代理（暂不支持）\n")
			continue
		} else if strings.HasPrefix(line, "trojan://") {
			fmt.Printf("  检测到 Trojan 代理（暂不支持）\n")
			continue
		} else {
			// 尝试解析为 JSON 格式
			proxy, err = up.parseJSONConfig(line)
			if err != nil {
				fmt.Printf("  未知格式，跳过: %v\n", err)
				continue
			}
		}
		
		if proxy != nil {
			proxies = append(proxies, proxy)
			fmt.Printf("  ✅ 成功解析: %s:%d [%s]\n", 
				proxy.GetAddress(), proxy.GetPort(), proxy.GetType())
		}
	}
	
	fmt.Printf("解析完成，共获得 %d 个有效代理\n", len(proxies))
	return proxies, nil
}

// parseSSURL 解析 Shadowsocks URL
func (up *UniversalProvider) parseSSURL(ssURL string) (Proxy, error) {
	if !strings.HasPrefix(ssURL, "ss://") {
		return nil, fmt.Errorf("invalid SS URL format")
	}
	
	// 解析 URL
	u, err := url.Parse(ssURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SS URL: %w", err)
	}
	
	// 获取用户信息部分
	userInfo := u.User.String()
	if userInfo == "" {
		// 尝试从 URL 的其他部分解析
		// 移除 ss:// 前缀
		remaining := ssURL[5:]
		
		// 查找 @ 符号
		atIndex := strings.LastIndex(remaining, "@")
		if atIndex == -1 {
			return nil, fmt.Errorf("invalid SS URL format: missing @")
		}
		
		userInfo = remaining[:atIndex]
		serverInfo := remaining[atIndex+1:]
		
		// 解析服务器信息
		parts := strings.Split(serverInfo, ":")
		if len(parts) < 2 {
			return nil, fmt.Errorf("invalid server format")
		}
		
		address := parts[0]
		portStr := parts[1]
		
		// 移除端口后的参数
		if idx := strings.Index(portStr, "/"); idx != -1 {
			portStr = portStr[:idx]
		}
		if idx := strings.Index(portStr, "?"); idx != -1 {
			portStr = portStr[:idx]
		}
		if idx := strings.Index(portStr, "#"); idx != -1 {
			portStr = portStr[:idx]
		}
		
		port := 0
		if _, err := fmt.Sscanf(portStr, "%d", &port); err != nil {
			return nil, fmt.Errorf("invalid port: %s", portStr)
		}
		
		// 解码用户信息
		decoded, err := base64.URLEncoding.DecodeString(userInfo)
		if err != nil {
			decoded, err = base64.StdEncoding.DecodeString(userInfo)
			if err != nil {
				return nil, fmt.Errorf("failed to decode user info: %w", err)
			}
		}
		
		// 解析方法和密码
		authParts := strings.Split(string(decoded), ":")
		if len(authParts) != 2 {
			return nil, fmt.Errorf("invalid auth format")
		}
		
		method := authParts[0]
		password := authParts[1]
		
		return NewShadowsocksProxy(address, port, password, method), nil
	}
	
	return nil, fmt.Errorf("SS URL parsing not fully implemented")
}

// parseJSONConfig 解析 JSON 格式配置
func (up *UniversalProvider) parseJSONConfig(jsonStr string) (Proxy, error) {
	// 这里可以实现 JSON 格式的代理配置解析
	return nil, fmt.Errorf("JSON config parsing not implemented")
}

// ValidateProxy 验证代理配置是否有效
func (up *UniversalProvider) ValidateProxy(proxy Proxy) error {
	if proxy == nil {
		return fmt.Errorf("proxy is nil")
	}
	
	// 根据代理类型进行相应的验证
	switch proxy.GetType() {
	case ProxyTypeShadowsocks:
		return NewShadowsocksProvider().ValidateProxy(proxy)
	case ProxyTypeVMess:
		return NewVMessProvider().ValidateProxy(proxy)
	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.GetType())
	}
}

// CreateProxy 从配置创建代理实例
func (up *UniversalProvider) CreateProxy(config map[string]interface{}) (Proxy, error) {
	proxyType, ok := config["type"].(string)
	if !ok {
		return nil, fmt.Errorf("missing proxy type")
	}
	
	switch ProxyType(proxyType) {
	case ProxyTypeShadowsocks:
		return NewShadowsocksProvider().CreateProxy(config)
	case ProxyTypeVMess:
		return NewVMessProvider().CreateProxy(config)
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxyType)
	}
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
