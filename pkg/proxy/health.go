package proxy

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"time"
)

// DefaultHealthChecker 默认健康检查器实现
type DefaultHealthChecker struct {
	timeout    time.Duration
	testURL    string
	httpClient *http.Client
}

// NewDefaultHealthChecker 创建默认健康检查器
func NewDefaultHealthChecker(timeout time.Duration) HealthChecker {
	return &DefaultHealthChecker{
		timeout: timeout,
		testURL: "http://www.google.com",
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// Check 检查代理健康状态
func (hc *DefaultHealthChecker) Check(ctx context.Context, proxy Proxy) error {
	if proxy == nil {
		return fmt.Errorf("proxy is nil")
	}
	
	// 根据代理类型选择检查方法
	switch proxy.GetType() {
	case ProxyTypeShadowsocks:
		return hc.checkShadowsocks(ctx, proxy)
	case ProxyTypeSSR:
		return hc.checkSSR(ctx, proxy)
	case ProxyTypeVMess:
		return hc.checkVMess(ctx, proxy)
	case ProxyTypeTrojan:
		return hc.checkTrojan(ctx, proxy)
	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.GetType())
	}
}

// GetTimeout 获取检查超时时间
func (hc *DefaultHealthChecker) GetTimeout() time.Duration {
	return hc.timeout
}

// SetTimeout 设置检查超时时间
func (hc *DefaultHealthChecker) SetTimeout(timeout time.Duration) {
	hc.timeout = timeout
	hc.httpClient.Timeout = timeout
}

// checkShadowsocks 检查 Shadowsocks 代理
func (hc *DefaultHealthChecker) checkShadowsocks(ctx context.Context, proxy Proxy) error {
	// 简单的 TCP 连接测试
	address := fmt.Sprintf("%s:%d", proxy.GetAddress(), proxy.GetPort())
	
	dialer := &net.Dialer{
		Timeout: hc.timeout,
	}
	
	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		return fmt.Errorf("failed to connect to proxy: %w", err)
	}
	defer conn.Close()
	
	// 设置读写超时
	if err := conn.SetDeadline(time.Now().Add(hc.timeout)); err != nil {
		return fmt.Errorf("failed to set deadline: %w", err)
	}
	
	// 这里可以添加更复杂的 Shadowsocks 协议验证
	// 目前只做基本的连接测试
	
	return nil
}

// checkSSR 检查 SSR 代理
func (hc *DefaultHealthChecker) checkSSR(ctx context.Context, proxy Proxy) error {
	// SSR 检查逻辑，目前使用与 SS 相同的方法
	return hc.checkShadowsocks(ctx, proxy)
}

// checkVMess 检查 VMess 代理
func (hc *DefaultHealthChecker) checkVMess(ctx context.Context, proxy Proxy) error {
	// VMess 检查逻辑
	address := fmt.Sprintf("%s:%d", proxy.GetAddress(), proxy.GetPort())
	
	dialer := &net.Dialer{
		Timeout: hc.timeout,
	}
	
	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		return fmt.Errorf("failed to connect to proxy: %w", err)
	}
	defer conn.Close()
	
	return nil
}

// checkTrojan 检查 Trojan 代理
func (hc *DefaultHealthChecker) checkTrojan(ctx context.Context, proxy Proxy) error {
	// Trojan 检查逻辑
	address := fmt.Sprintf("%s:%d", proxy.GetAddress(), proxy.GetPort())
	
	dialer := &net.Dialer{
		Timeout: hc.timeout,
	}
	
	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		return fmt.Errorf("failed to connect to proxy: %w", err)
	}
	defer conn.Close()
	
	return nil
}

// HTTPHealthChecker HTTP 健康检查器
type HTTPHealthChecker struct {
	timeout    time.Duration
	testURL    string
	httpClient *http.Client
}

// NewHTTPHealthChecker 创建 HTTP 健康检查器
func NewHTTPHealthChecker(timeout time.Duration, testURL string) HealthChecker {
	if testURL == "" {
		testURL = "http://www.google.com"
	}
	
	return &HTTPHealthChecker{
		timeout: timeout,
		testURL: testURL,
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// Check 通过 HTTP 请求检查代理健康状态
func (hc *HTTPHealthChecker) Check(ctx context.Context, proxy Proxy) error {
	if proxy == nil {
		return fmt.Errorf("proxy is nil")
	}
	
	// 创建代理 URL
	proxyURL, err := hc.createProxyURL(proxy)
	if err != nil {
		return fmt.Errorf("failed to create proxy URL: %w", err)
	}
	
	// 创建带代理的 HTTP 客户端
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
		DialContext: (&net.Dialer{
			Timeout: hc.timeout,
		}).DialContext,
	}
	
	client := &http.Client{
		Transport: transport,
		Timeout:   hc.timeout,
	}
	
	// 发送测试请求
	req, err := http.NewRequestWithContext(ctx, "GET", hc.testURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("proxy request failed: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode >= 400 {
		return fmt.Errorf("proxy request returned status: %d", resp.StatusCode)
	}
	
	return nil
}

// GetTimeout 获取检查超时时间
func (hc *HTTPHealthChecker) GetTimeout() time.Duration {
	return hc.timeout
}

// SetTimeout 设置检查超时时间
func (hc *HTTPHealthChecker) SetTimeout(timeout time.Duration) {
	hc.timeout = timeout
	hc.httpClient.Timeout = timeout
}

// createProxyURL 创建代理 URL
func (hc *HTTPHealthChecker) createProxyURL(proxy Proxy) (*url.URL, error) {
	config := proxy.GetConfig()
	
	switch proxy.GetType() {
	case ProxyTypeShadowsocks:
		// Shadowsocks 代理 URL 格式
		password, ok := config["password"].(string)
		if !ok {
			return nil, fmt.Errorf("missing password")
		}
		
		method, ok := config["method"].(string)
		if !ok {
			return nil, fmt.Errorf("missing method")
		}
		
		// 注意：这里简化了 SS 代理的 URL 格式
		// 实际使用时可能需要更复杂的处理
		return url.Parse(fmt.Sprintf("socks5://%s:%s@%s:%d",
			method, password, proxy.GetAddress(), proxy.GetPort()))
		
	case ProxyTypeSSR:
		// SSR 代理 URL 格式
		return url.Parse(fmt.Sprintf("socks5://%s:%d", proxy.GetAddress(), proxy.GetPort()))
		
	case ProxyTypeVMess:
		// VMess 代理 URL 格式
		return url.Parse(fmt.Sprintf("socks5://%s:%d", proxy.GetAddress(), proxy.GetPort()))
		
	case ProxyTypeTrojan:
		// Trojan 代理 URL 格式
		return url.Parse(fmt.Sprintf("socks5://%s:%d", proxy.GetAddress(), proxy.GetPort()))
		
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxy.GetType())
	}
}

// RandomSelector 随机选择器
type RandomSelector struct{}

// NewRandomSelector 创建随机选择器
func NewRandomSelector() ProxySelector {
	return &RandomSelector{}
}

// Select 随机选择代理
func (rs *RandomSelector) Select(proxies []Proxy) (Proxy, error) {
	if len(proxies) == 0 {
		return nil, ErrNoHealthyProxy
	}
	
	// 使用时间作为随机种子
	index := time.Now().UnixNano() % int64(len(proxies))
	return proxies[index].Clone(), nil
}

// RoundRobinSelector 轮询选择器
type RoundRobinSelector struct {
	index int
}

// NewRoundRobinSelector 创建轮询选择器
func NewRoundRobinSelector() ProxySelector {
	return &RoundRobinSelector{}
}

// Select 轮询选择代理
func (rrs *RoundRobinSelector) Select(proxies []Proxy) (Proxy, error) {
	if len(proxies) == 0 {
		return nil, ErrNoHealthyProxy
	}
	
	proxy := proxies[rrs.index%len(proxies)]
	rrs.index++
	
	return proxy.Clone(), nil
}

// HealthyFilter 健康代理过滤器
type HealthyFilter struct{}

// NewHealthyFilter 创建健康代理过滤器
func NewHealthyFilter() ProxyFilter {
	return &HealthyFilter{}
}

// Filter 过滤出健康的代理
func (hf *HealthyFilter) Filter(proxies []Proxy) []Proxy {
	var result []Proxy
	for _, proxy := range proxies {
		if proxy.IsHealthy() {
			result = append(result, proxy)
		}
	}
	return result
}

// TypeFilter 类型过滤器
type TypeFilter struct {
	allowedTypes []ProxyType
}

// NewTypeFilter 创建类型过滤器
func NewTypeFilter(allowedTypes ...ProxyType) ProxyFilter {
	return &TypeFilter{
		allowedTypes: allowedTypes,
	}
}

// Filter 按类型过滤代理
func (tf *TypeFilter) Filter(proxies []Proxy) []Proxy {
	if len(tf.allowedTypes) == 0 {
		return proxies
	}
	
	var result []Proxy
	for _, proxy := range proxies {
		for _, allowedType := range tf.allowedTypes {
			if proxy.GetType() == allowedType {
				result = append(result, proxy)
				break
			}
		}
	}
	return result
}
